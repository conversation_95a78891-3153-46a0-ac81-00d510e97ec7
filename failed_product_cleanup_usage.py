#!/usr/bin/env python3
"""
Usage example for failed product section cleanup functionality

This script demonstrates how to use the enhanced CleanupProcessor to handle
AAWP plugin failures in Amazon affiliate integration.

Two main failure scenarios are handled:
1. Scenario 1: AAWP deactivated/crashed (handled by existing visible shortcode cleanup)
2. Scenario 2b: AAWP active but no products found (handled by new failed section cleanup)
"""

import asyncio
import logging
from typing import List

from amazon_affiliate_integration.processors.cleanup_processor import CleanupProcessor
from amazon_affiliate_integration.clients.wordpress_client import WordPressClient
from amazon_affiliate_integration.processors.state_manager import StateManager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def example_scenario_1_cleanup():
    """
    Example: Cleanup when AAWP is deactivated/crashed (Scenario 1)
    
    This scenario is handled by the existing cleanup_failed_shortcodes() method
    which removes visible shortcodes detected by crawlers.
    """
    print("\n=== Scenario 1: AAWP Deactivated/Crashed Cleanup ===")
    
    # Initialize components (you would use real instances)
    wordpress_client = WordPressClient()  # Your WordPress client
    state_manager = StateManager()        # Your state manager
    cleanup_processor = CleanupProcessor(wordpress_client, state_manager)
    
    # This would come from your crawler results
    # crawler_results = await your_crawler.crawl_articles(article_urls)
    
    # Example: Process cleanup for articles with visible shortcodes
    # results = await cleanup_processor.cleanup_failed_shortcodes(crawler_results)
    
    print("Scenario 1 is handled by existing cleanup_failed_shortcodes() method")
    print("It removes the entire PRODUCT_SECTION_TEMPLATE when visible shortcodes are detected")
    print("✅ No additional code needed - existing functionality handles this correctly")

async def example_scenario_2b_cleanup():
    """
    Example: Cleanup when AAWP is active but no products found (Scenario 2b)
    
    This scenario is handled by the new cleanup_failed_product_sections_batch() method
    which detects orphaned H3 headings followed immediately by paragraphs.
    """
    print("\n=== Scenario 2b: AAWP Active But No Products Found ===")
    
    # Initialize components (you would use real instances)
    wordpress_client = WordPressClient()  # Your WordPress client
    state_manager = StateManager()        # Your state manager
    cleanup_processor = CleanupProcessor(wordpress_client, state_manager)
    
    # List of article URLs to check for failed product sections
    article_urls = [
        "https://cozytones.com/teen-bedroom-decor-ideas/",
        "https://example.com/another-article/",
        # Add more URLs as needed
    ]
    
    try:
        # Process cleanup for failed product sections
        results = await cleanup_processor.cleanup_failed_product_sections_batch(article_urls)
        
        # Process results
        successful_cleanups = [r for r in results if r.success]
        failed_cleanups = [r for r in results if not r.success]
        
        print(f"Successfully processed: {len(successful_cleanups)} articles")
        print(f"Failed to process: {len(failed_cleanups)} articles")
        
        # Show statistics
        stats = cleanup_processor.get_statistics()
        print(f"Statistics: {stats}")
        
        return results
        
    except Exception as e:
        logger.error(f"Error during Scenario 2b cleanup: {e}")
        return []

async def example_comprehensive_cleanup():
    """
    Example: Comprehensive cleanup for both scenarios
    
    This demonstrates how to handle both failure scenarios in a complete workflow.
    """
    print("\n=== Comprehensive Cleanup Workflow ===")
    
    # Initialize components
    wordpress_client = WordPressClient()  # Your WordPress client
    state_manager = StateManager()        # Your state manager
    cleanup_processor = CleanupProcessor(wordpress_client, state_manager)
    
    # Step 1: Crawl articles to detect visible shortcodes (Scenario 1)
    article_urls = [
        "https://cozytones.com/teen-bedroom-decor-ideas/",
        "https://example.com/another-article/",
    ]
    
    print("Step 1: Crawling articles to detect visible shortcodes...")
    # crawler_results = await your_crawler.crawl_articles(article_urls)
    # scenario_1_results = await cleanup_processor.cleanup_failed_shortcodes(crawler_results)
    
    # Step 2: Clean up failed product sections (Scenario 2b)
    print("Step 2: Cleaning up failed product sections...")
    scenario_2b_results = await cleanup_processor.cleanup_failed_product_sections_batch(article_urls)
    
    # Combine results and report
    print(f"Scenario 2b cleanup completed: {len(scenario_2b_results)} articles processed")
    
    # Show final statistics
    stats = cleanup_processor.get_statistics()
    print(f"Final statistics: {stats}")
    
    return scenario_2b_results

def example_content_only_cleanup():
    """
    Example: Direct content cleanup without WordPress API calls
    
    This shows how to use the cleanup methods directly on content strings
    for testing or batch processing scenarios.
    """
    print("\n=== Direct Content Cleanup Example ===")
    
    # Sample content with failed product section (Scenario 2b)
    sample_content = '''
<!-- wp:paragraph -->
<p>Here's some regular content.</p>
<!-- /wp:paragraph -->

<!-- wp:heading {"level":3} -->
<h3 class="wp-block-heading">Recommended Products to replicate this idea</h3>
<!-- /wp:heading -->

<!-- wp:paragraph -->
<p>This paragraph immediately follows the H3, indicating the shortcode disappeared.</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph -->
<p>More content continues here.</p>
<!-- /wp:paragraph -->
'''
    
    # Initialize processor (mock objects for content-only processing)
    cleanup_processor = CleanupProcessor(None, None)
    
    # Clean up failed product sections
    cleaned_content, sections_removed = cleanup_processor.cleanup_failed_product_sections(sample_content)
    
    print(f"Original content length: {len(sample_content)}")
    print(f"Cleaned content length: {len(cleaned_content)}")
    print(f"Sections removed: {sections_removed}")
    
    if sections_removed > 0:
        print("✅ Successfully removed failed product sections")
        print("\nCleaned content preview:")
        print(cleaned_content[:200] + "..." if len(cleaned_content) > 200 else cleaned_content)
    else:
        print("ℹ️ No failed product sections found")

async def main():
    """Main example function"""
    print("Failed Product Section Cleanup - Usage Examples")
    print("=" * 60)
    
    try:
        # Run examples
        await example_scenario_1_cleanup()
        await example_scenario_2b_cleanup()
        await example_comprehensive_cleanup()
        example_content_only_cleanup()
        
        print("\n" + "=" * 60)
        print("All examples completed successfully!")
        
    except Exception as e:
        logger.error(f"Example execution failed: {e}")
        raise

if __name__ == "__main__":
    # Run the examples
    asyncio.run(main())
