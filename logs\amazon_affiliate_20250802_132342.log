2025-08-02 13:23:42,381 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_132342.log
2025-08-02 13:23:42,382 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_132342.log
2025-08-02 13:23:42,382 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-08-02 13:23:42,383 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-08-02 13:23:42,383 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-08-02 13:23:42,383 - __main__ - INFO - Starting complete workflow: Process → Crawl → Cleanup...
2025-08-02 13:23:42,383 - amazon_affiliate_integration - INFO - 🚀 Starting COMPLETE workflow: Process → Crawl → Cleanup
2025-08-02 13:23:42,385 - amazon_affiliate_integration - INFO - 📝 Phase 1: Processing articles to add Amazon shortcodes...
2025-08-02 13:23:42,385 - amazon_affiliate_integration - INFO - Starting domain processing: cozytones.com (force=False, dry_run=False, limit=1)
2025-08-02 13:23:42,385 - amazon_affiliate_integration - INFO - Loaded 0 previously processed URLs
2025-08-02 13:23:42,385 - amazon_affiliate_integration - INFO - Loaded 0 excluded URLs
2025-08-02 13:23:42,386 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress client initialized for cozytones.com (https://cozytones.com)
2025-08-02 13:23:43,867 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?per_page=1 "HTTP/1.1 200 OK"
2025-08-02 13:23:43,870 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress API connection test successful for cozytones.com
2025-08-02 13:23:43,870 - amazon_affiliate_integration - INFO - Fetching articles from cozytones.com...
2025-08-02 13:23:43,870 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetching articles from cozytones.com since 2025-01-01T00:00:00
2025-08-02 13:23:46,551 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?page=1&per_page=100&after=2025-01-01T00%3A00%3A00&status=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-08-02 13:23:48,143 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetched 100 articles from page 1
2025-08-02 13:23:48,143 - amazon_affiliate_integration.clients.wordpress_client - INFO - Limited to 1 articles for testing
2025-08-02 13:23:48,143 - amazon_affiliate_integration.clients.wordpress_client - INFO - Total articles fetched from cozytones.com: 1
2025-08-02 13:23:48,146 - amazon_affiliate_integration - INFO - Converted 1 articles to ArticleInfo objects
2025-08-02 13:23:48,146 - amazon_affiliate_integration - INFO - Normal mode: processing 1 articles (skipping 0 processed, 0 excluded)
2025-08-02 13:23:48,464 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Pre-processing checkpoint for cozytones.com (1 articles)
2025-08-02 13:23:48,465 - amazon_affiliate_integration - INFO - Created git checkpoint before processing cozytones.com
2025-08-02 13:23:48,465 - amazon_affiliate_integration - INFO - Processing 1 articles for cozytones.com...
2025-08-02 13:23:48,465 - amazon_affiliate_integration.processors.content_processor - INFO - Starting batch processing of 1 articles with concurrency limit 4
2025-08-02 13:23:48,465 - amazon_affiliate_integration.processors.content_processor - INFO - Processing article 304: 12 Modern Farmhouse Bedroom Ideas for Rustic Elegance
2025-08-02 13:23:48,467 - amazon_affiliate_integration.utils.backup_manager - INFO - Created backup: cozytones.com_304_pre_processing_20250802_132348.json
2025-08-02 13:23:48,468 - amazon_affiliate_integration.processors.content_processor - INFO - Generating products for 12 sections using batch processing
2025-08-02 13:23:48,468 - amazon_affiliate_integration.processors.content_processor - INFO - Using individual processing for reliable product generation
2025-08-02 13:23:50,269 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:23:50,271 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '1. Reclaimed Wood Headboard with Weathered Finish': ['Reclaimed Wood Headboard', 'Rustic Wooden Bed Frame']
2025-08-02 13:23:51,747 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:23:51,750 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '2. Industrial-Style Lighting with Matte Black Fixtures': ['Industrial Matte Black Pendant Lights', 'Matte Black Wall Sconces']
2025-08-02 13:23:52,836 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:23:52,837 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '3. Linen Bedding in Soft Neutral Tones': ['Linen Duvet Cover Set', 'Luxurious Wool Throw Blanket']
2025-08-02 13:23:54,412 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:23:54,417 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '4. Vintage-Inspired Nightstands with Distressed Paint': ['Vintage Distressed Wooden Nightstand', 'Antique-Style Painted Bedroom Nightstand']
2025-08-02 13:23:55,837 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:23:55,838 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '5. Neutral Area Rug with Subtle Pattern': ['Shaw Rugs Flokati Wool Area Rug', "Safavieh Adirondack Collection 8' x 10' Area Rug"]
2025-08-02 13:23:57,257 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:23:57,258 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '6. Classic Barn Door Sliding Closet Entrance': ['Reclaimed Wood Barn Door', 'Black Iron Sliding Door Hardware Kit']
2025-08-02 13:23:59,654 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:23:59,655 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '7. Minimalist Modern Bed Frame in Matte Finish': ['Modern Matte Black Metal Bed Frame', 'Minimalist Low-Profile Platform Bed Frame']
2025-08-02 13:24:01,510 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:24:01,511 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '8. Mason Jar or Rustic Lanterns for Ambient Lighting': ['Mason Jar String Lights with Edison Bulbs', 'Vintage Metal Frame Lanterns with Candle-Like LED Bulbs']
2025-08-02 13:24:02,807 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:24:02,808 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '9. Simple Linen-Covered Window Treatments': ['Linen Window Curtain Panels', 'Adjustable Metal Curtain Rods']
2025-08-02 13:24:03,789 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:24:03,790 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '10. Layered Bedding with Cozy Throws and Pillows': ['Luxury Chunky Knit Throw Blanket', 'Set of Designer Velvet Throw Pillows']
2025-08-02 13:24:04,868 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:24:04,874 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '11. Framed Vintage Botanical Prints or Sketches': ['Framed Vintage Botanical Prints', 'Distressed Wood Frame for Artwork']
2025-08-02 13:24:06,563 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:24:06,564 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '12. Practical yet Stylish Storage Solutions with Open Wardrobes': ['Wooden Clothing Rack with Distressed Finish', 'Open Wooden Shelving Unit for Storage']
2025-08-02 13:24:06,674 - amazon_affiliate_integration.processors.content_processor - INFO - Inserted 24 product sections into content
2025-08-02 13:24:06,674 - amazon_affiliate_integration.processors.content_processor - INFO - Successfully processed article 304 in 18.21s. Added 24 shortcodes for 12 sections.
2025-08-02 13:24:06,674 - amazon_affiliate_integration.processors.content_processor - INFO - Batch processing completed: 1 successful, 0 failed, 0 skipped
2025-08-02 13:24:06,674 - amazon_affiliate_integration - INFO - Updating WordPress content for cozytones.com...
2025-08-02 13:24:06,674 - amazon_affiliate_integration.clients.wordpress_client - INFO - Starting batch update of 1 articles on cozytones.com
2025-08-02 13:24:08,244 - httpx - INFO - HTTP Request: POST https://cozytones.com/wp-json/wp/v2/posts/304 "HTTP/1.1 200 OK"
2025-08-02 13:24:08,249 - amazon_affiliate_integration.clients.wordpress_client - INFO - Successfully updated article 304 on cozytones.com
2025-08-02 13:24:08,249 - amazon_affiliate_integration.clients.wordpress_client - INFO - Batch update completed on cozytones.com: 1 successful, 0 failed
2025-08-02 13:24:08,250 - amazon_affiliate_integration - INFO - WordPress updates completed: 1 successful, 0 failed
2025-08-02 13:24:08,252 - amazon_affiliate_integration - INFO - Updated state with 1 newly processed URLs
2025-08-02 13:24:08,483 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Post-processing checkpoint for cozytones.com (1 articles processed)
2025-08-02 13:24:08,484 - amazon_affiliate_integration - INFO - Domain processing completed for cozytones.com: 1 processed, 0 failed, 0 skipped in 26.10s
2025-08-02 13:24:08,484 - amazon_affiliate_integration - INFO - ✅ Phase 1 completed successfully
2025-08-02 13:24:08,484 - amazon_affiliate_integration - INFO - 🕷️ Phase 2 & 3: Crawling and cleaning up failed shortcodes...
2025-08-02 13:24:08,484 - amazon_affiliate_integration - INFO - Starting crawl and cleanup workflow...
2025-08-02 13:24:08,485 - amazon_affiliate_integration - INFO - Phase 1: Crawling articles for visible shortcodes...
2025-08-02 13:24:08,486 - amazon_affiliate_integration - INFO - Using browser crawler for cozytones.com
2025-08-02 13:24:09,028 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser initialized with 4 concurrent pages
2025-08-02 13:24:09,029 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Starting browser crawl of 1 articles...
2025-08-02 13:24:14,940 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Progress: 1/1 articles crawled
2025-08-02 13:24:14,940 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser crawl completed: 1/1 successful in 5.91s (0.2 articles/sec)
2025-08-02 13:24:15,059 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser resources cleaned up
2025-08-02 13:24:15,059 - amazon_affiliate_integration - INFO - Crawled 1 articles for domain cozytones.com, found 24 visible shortcodes
2025-08-02 13:24:15,062 - amazon_affiliate_integration - INFO - Phase 2: Cleaning up failed shortcodes...
2025-08-02 13:24:15,062 - amazon_affiliate_integration.processors.cleanup_processor - INFO - Starting cleanup for 1 articles with visible shortcodes
2025-08-02 13:24:15,685 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?slug=modern-farmhouse-bedroom-ideas&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-08-02 13:24:16,392 - httpx - INFO - HTTP Request: POST https://cozytones.com/wp-json/wp/v2/posts/304 "HTTP/1.1 200 OK"
2025-08-02 13:24:16,397 - amazon_affiliate_integration.clients.wordpress_client - INFO - Successfully updated article 304 on cozytones.com
2025-08-02 13:24:16,401 - amazon_affiliate_integration.processors.cleanup_processor - INFO - Successfully cleaned up 23 shortcodes from https://cozytones.com/modern-farmhouse-bedroom-ideas/
2025-08-02 13:24:16,401 - amazon_affiliate_integration.processors.cleanup_processor - INFO - Cleaned up 1/1 articles (23 shortcodes removed)
2025-08-02 13:24:16,402 - amazon_affiliate_integration.processors.cleanup_processor - INFO - Cleanup completed: {'articles_processed': 1, 'shortcodes_removed': 23, 'cleanup_failures': 0, 'articles_updated': 1}
2025-08-02 13:24:16,402 - amazon_affiliate_integration - INFO - Crawl and cleanup workflow completed successfully in 7.92s
2025-08-02 13:24:16,402 - amazon_affiliate_integration - INFO - ✅ Phase 2 & 3 completed successfully
2025-08-02 13:24:16,402 - amazon_affiliate_integration - ERROR - ❌ Complete workflow failed: 'CleanupResult' object has no attribute 'get'
2025-08-02 13:24:16,402 - __main__ - ERROR - Complete workflow failed
