2025-08-02 10:15:27,069 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_101527.log
2025-08-02 10:15:27,070 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_101527.log
2025-08-02 10:15:27,070 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-08-02 10:15:27,071 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-08-02 10:15:27,072 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-08-02 10:15:27,072 - __main__ - INFO - Starting complete workflow: Process → Crawl → Cleanup...
2025-08-02 10:15:27,072 - amazon_affiliate_integration - INFO - 🚀 Starting COMPLETE workflow: Process → Crawl → Cleanup
2025-08-02 10:15:27,073 - amazon_affiliate_integration - INFO - 📝 Phase 1: Processing articles to add Amazon shortcodes...
2025-08-02 10:15:27,073 - amazon_affiliate_integration - INFO - Starting domain processing: cozytones.com (force=False, dry_run=False, limit=1)
2025-08-02 10:15:27,073 - amazon_affiliate_integration - INFO - Loaded 0 previously processed URLs
2025-08-02 10:15:27,073 - amazon_affiliate_integration - INFO - Loaded 0 excluded URLs
2025-08-02 10:15:27,073 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress client initialized for cozytones.com (https://cozytones.com)
2025-08-02 10:15:28,602 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?per_page=1 "HTTP/1.1 200 OK"
2025-08-02 10:15:28,603 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress API connection test successful for cozytones.com
2025-08-02 10:15:28,604 - amazon_affiliate_integration - INFO - Fetching articles from cozytones.com...
2025-08-02 10:15:28,604 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetching articles from cozytones.com since 2025-01-01T00:00:00
2025-08-02 10:15:31,654 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?page=1&per_page=100&after=2025-01-01T00%3A00%3A00&status=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-08-02 10:15:33,131 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetched 100 articles from page 1
2025-08-02 10:15:33,131 - amazon_affiliate_integration.clients.wordpress_client - INFO - Limited to 1 articles for testing
2025-08-02 10:15:33,132 - amazon_affiliate_integration.clients.wordpress_client - INFO - Total articles fetched from cozytones.com: 1
2025-08-02 10:15:33,134 - amazon_affiliate_integration - INFO - Converted 1 articles to ArticleInfo objects
2025-08-02 10:15:33,134 - amazon_affiliate_integration - INFO - Normal mode: processing 1 articles (skipping 0 processed, 0 excluded)
2025-08-02 10:15:33,392 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Pre-processing checkpoint for cozytones.com (1 articles)
2025-08-02 10:15:33,392 - amazon_affiliate_integration - INFO - Created git checkpoint before processing cozytones.com
2025-08-02 10:15:33,392 - amazon_affiliate_integration - INFO - Processing 1 articles for cozytones.com...
2025-08-02 10:15:33,393 - amazon_affiliate_integration.processors.content_processor - INFO - Starting batch processing of 1 articles with concurrency limit 4
2025-08-02 10:15:33,393 - amazon_affiliate_integration.processors.content_processor - INFO - Processing article 304: 12 Modern Farmhouse Bedroom Ideas for Rustic Elegance
2025-08-02 10:15:33,397 - amazon_affiliate_integration.utils.backup_manager - INFO - Created backup: cozytones.com_304_pre_processing_20250802_101533.json
2025-08-02 10:15:33,398 - amazon_affiliate_integration.processors.content_processor - INFO - Generating products for 12 sections using batch processing
2025-08-02 10:15:33,398 - amazon_affiliate_integration.processors.content_processor - INFO - Using individual processing for reliable product generation
2025-08-02 10:15:34,939 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:15:34,940 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '1. Reclaimed Wood Headboard with Weathered Finish': ['Reclaimed Wood Headboard', 'Rustic Wooden Bed Frame']
2025-08-02 10:15:36,410 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:15:36,411 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '2. Industrial-Style Lighting with Matte Black Fixtures': ['Kichler 43023OZ Toman 1-Light Pendant Light', 'Westinghouse Iron Hill Wall Sconce Lighting Fixture']
2025-08-02 10:15:38,292 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:15:38,299 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '3. Linen Bedding in Soft Neutral Tones': ['Linenspa 3 Piece Duvet Cover Set Queen - Soft Neutral Beige Linen Bedding', 'Barefoot Dreams CozyChic Throw Blanket in Neutral Tones']
2025-08-02 10:15:39,829 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:15:39,841 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '4. Vintage-Inspired Nightstands with Distressed Paint': ['Vintage Distressed Nightstand', 'Pastel Painted Wooden Nightstand']
2025-08-02 10:15:44,143 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:15:44,152 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '5. Neutral Area Rug with Subtle Pattern': ['Wool Area Rug', 'Handwoven Berber Cotton Flatweave Rug']
2025-08-02 10:15:45,689 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:15:45,691 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '6. Classic Barn Door Sliding Closet Entrance': ['Reclaimed Wood Barn Door', 'Black Iron Hardware Sliding Barn Door Kit']
2025-08-02 10:15:46,761 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:15:46,762 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '7. Minimalist Modern Bed Frame in Matte Finish': ['Modern Minimalist Bed Frame in Matte Black or Gray', 'Upholstered Textured Bedding Set in Layered Textiles']
2025-08-02 10:15:47,939 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:15:47,941 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '8. Mason Jar or Rustic Lanterns for Ambient Lighting': ['Vintage Mason Jar Pendant Light Fixtures', 'Rustic Metal Lantern Table Lamps']
2025-08-02 10:15:49,440 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:15:49,442 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '9. Simple Linen-Covered Window Treatments': ['Linen Window Curtain Panels', 'Adjustable Metal Curtain Rods']
2025-08-02 10:15:50,838 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:15:50,842 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '10. Layered Bedding with Cozy Throws and Pillows': ['Luxury Chunky Knit Throw Blanket', 'Set of Decorative Velvet Throw Pillows']
2025-08-02 10:15:51,871 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:15:51,876 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '11. Framed Vintage Botanical Prints or Sketches': ['Vintage Botanical Framed Art Prints', 'Distressed Wooden Picture Frames for Wall Art']
2025-08-02 10:15:53,030 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:15:53,031 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '12. Practical yet Stylish Storage Solutions with Open Wardrobes': ['Freestanding Wooden Clothing Rack with Distressed Finish', 'Open-Design Wooden Shelving Unit for Wardrobe Storage']
2025-08-02 10:15:53,151 - amazon_affiliate_integration.processors.content_processor - INFO - Inserted 12 product sections into content
2025-08-02 10:15:53,151 - amazon_affiliate_integration.processors.content_processor - INFO - Successfully processed article 304 in 19.76s. Added 12 shortcodes for 12 sections.
2025-08-02 10:15:53,153 - amazon_affiliate_integration.processors.content_processor - INFO - Batch processing completed: 1 successful, 0 failed, 0 skipped
2025-08-02 10:15:53,153 - amazon_affiliate_integration - INFO - Updating WordPress content for cozytones.com...
2025-08-02 10:15:53,153 - amazon_affiliate_integration.clients.wordpress_client - INFO - Starting batch update of 1 articles on cozytones.com
2025-08-02 10:15:54,117 - httpx - INFO - HTTP Request: POST https://cozytones.com/wp-json/wp/v2/posts/304 "HTTP/1.1 200 OK"
2025-08-02 10:15:54,123 - amazon_affiliate_integration.clients.wordpress_client - INFO - Successfully updated article 304 on cozytones.com
2025-08-02 10:15:54,124 - amazon_affiliate_integration.clients.wordpress_client - INFO - Batch update completed on cozytones.com: 1 successful, 0 failed
2025-08-02 10:15:54,124 - amazon_affiliate_integration - INFO - WordPress updates completed: 1 successful, 0 failed
2025-08-02 10:15:54,126 - amazon_affiliate_integration - INFO - Updated state with 1 newly processed URLs
2025-08-02 10:15:54,349 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Post-processing checkpoint for cozytones.com (1 articles processed)
2025-08-02 10:15:54,349 - amazon_affiliate_integration - INFO - Domain processing completed for cozytones.com: 1 processed, 0 failed, 0 skipped in 27.28s
2025-08-02 10:15:54,349 - amazon_affiliate_integration - INFO - ✅ Phase 1 completed successfully
2025-08-02 10:15:54,350 - amazon_affiliate_integration - INFO - 🕷️ Phase 2 & 3: Crawling and cleaning up failed shortcodes...
2025-08-02 10:15:54,350 - amazon_affiliate_integration - INFO - Starting crawl and cleanup workflow...
2025-08-02 10:15:54,350 - amazon_affiliate_integration - INFO - Phase 1: Crawling articles for visible shortcodes...
2025-08-02 10:15:54,351 - amazon_affiliate_integration - INFO - Using browser crawler for cozytones.com
2025-08-02 10:15:55,461 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser initialized with 4 concurrent pages
2025-08-02 10:15:55,461 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Starting browser crawl of 1 articles...
2025-08-02 10:16:02,798 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Progress: 1/1 articles crawled
2025-08-02 10:16:02,798 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser crawl completed: 1/1 successful in 7.34s (0.1 articles/sec)
2025-08-02 10:16:02,923 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser resources cleaned up
2025-08-02 10:16:02,923 - amazon_affiliate_integration - INFO - Crawled 1 articles for domain cozytones.com, found 0 visible shortcodes
2025-08-02 10:16:02,923 - amazon_affiliate_integration - INFO - Phase 2: Cleaning up failed shortcodes...
2025-08-02 10:16:02,924 - amazon_affiliate_integration - INFO - Crawl and cleanup workflow completed successfully in 8.57s
2025-08-02 10:16:02,924 - amazon_affiliate_integration - INFO - ✅ Phase 2 & 3 completed successfully
2025-08-02 10:16:02,924 - amazon_affiliate_integration - INFO - 🎉 COMPLETE workflow finished successfully in 35.85s
2025-08-02 10:16:02,924 - __main__ - INFO - Complete workflow finished successfully
