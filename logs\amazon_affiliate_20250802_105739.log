2025-08-02 10:57:39,511 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_105739.log
2025-08-02 10:57:39,512 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_105739.log
2025-08-02 10:57:39,512 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-08-02 10:57:39,512 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-08-02 10:57:39,512 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-08-02 10:57:39,522 - __main__ - INFO - Starting crawl and cleanup workflow...
2025-08-02 10:57:39,522 - amazon_affiliate_integration - INFO - Starting crawl and cleanup workflow...
2025-08-02 10:57:39,522 - amazon_affiliate_integration - INFO - Phase 1: Crawling articles for visible shortcodes...
2025-08-02 10:57:39,529 - amazon_affiliate_integration - INFO - Using browser crawler for cozytones.com
2025-08-02 10:57:40,027 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser initialized with 4 concurrent pages
2025-08-02 10:57:40,028 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Starting browser crawl of 1 articles...
2025-08-02 10:57:43,207 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Content snippet for shortcode detection: <!DOCTYPE html><html lang="en-US" data-scroll="0"><head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>12 Modern Farmhouse Bedroom Ideas for Rustic Elegance</title>
<meta name="robots" content="max-image-preview:large">
	<style>img:is([sizes="auto" i], [sizes^="auto," i]) { contain-intrinsic-size: 3000px 1500px }</style>
	<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link href="https://fonts.gstatic.com" crossorigin="" rel="preconnect">
<link rel="alternate" type="application/rss+xml" title="Cozy Tones » Feed" href="https://cozytones.com/feed/">
<link rel="alternate" type="application/rss+xml" title="Cozy Tones » Comments Feed" href="https://cozytones.com/comments/feed/">
<link rel="alternate" type="application/rss+xml" title="Cozy Tones » 12 Modern Farmhouse Bedroom Ideas for Rustic Elegance Comments Feed" href="https://cozytones.com/modern-farmhouse-bedroom-ideas/feed/">
<link rel="canonical" href="https://cozytones.com/modern-farmhouse-bedroom-ideas/">
<script>
window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/16.0.1\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/16.0.1\/svg\/","svgExt":".svg","source":{"concatemoji":"https:\/\/cozytones.com\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.2"}};
/*! This file is auto-generated */
!function(s,n){var o,i,e;function c(e){try{var t={supportTests:e,timestamp:(new Date).valueOf()};sessionStorage.setItem(o,JSON.stringify(t))}catch(e){}}function p(e,t,n){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);var t=new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data),a=(e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(n,0,0),new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data));return t.every(function(e,t){return e===a[t]})}function u(e,t){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);for(var n=e.getImageData(16,16,1,1),a=0;a
2025-08-02 10:57:43,207 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Content contains 12 '[amazon bestseller=' strings
2025-08-02 10:57:43,208 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Regex pattern found 12 matches in HTML
2025-08-02 10:57:43,211 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found 12 shortcodes in HTML, 12 in text, 12 unique
2025-08-02 10:57:43,211 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Reclaimed Wood Headboard' under heading: 'Recommended Products to replicate this idea' at position 26976
2025-08-02 10:57:43,212 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Westinghouse Iron Hill 1-Light Pendant Light' under heading: 'Recommended Products to replicate this idea' at position 30250
2025-08-02 10:57:43,212 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Linen Duvet Cover Set' under heading: 'Recommended Products to replicate this idea' at position 33371
2025-08-02 10:57:43,212 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Vintage Distressed Nightstand' under heading: 'Recommended Products to replicate this idea' at position 36362
2025-08-02 10:57:43,213 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'NuLOOM Moroccan Blythe Area Rug' under heading: 'Recommended Products to replicate this idea' at position 39351
2025-08-02 10:57:43,213 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Reclaimed Wood Barn Door' under heading: 'Recommended Products to replicate this idea' at position 42256
2025-08-02 10:57:43,213 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Modern Matte Black Metal Bed Frame' under heading: 'Recommended Products to replicate this idea' at position 45026
2025-08-02 10:57:43,213 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Vintage Mason Jar Pendant Light Fixtures' under heading: 'Recommended Products to replicate this idea' at position 47921
2025-08-02 10:57:43,213 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Linen Window Curtains' under heading: 'Recommended Products to replicate this idea' at position 50976
2025-08-02 10:57:43,213 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Linen Throw Pillow Set' under heading: 'Recommended Products to replicate this idea' at position 53789
2025-08-02 10:57:43,213 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Vintage Botanical Framed Art Prints' under heading: 'Recommended Products to replicate this idea' at position 56645
2025-08-02 10:57:43,214 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Wooden Clothing Rack with Distressed Finish' under heading: 'Recommended Products to replicate this idea' at position 59510
2025-08-02 10:57:43,218 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Progress: 1/1 articles crawled
2025-08-02 10:57:43,218 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser crawl completed: 1/1 successful in 3.19s (0.3 articles/sec)
2025-08-02 10:57:43,306 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser resources cleaned up
2025-08-02 10:57:43,306 - amazon_affiliate_integration - INFO - Crawled 1 articles for domain cozytones.com, found 12 visible shortcodes
2025-08-02 10:57:43,309 - amazon_affiliate_integration.processors.state_manager - DEBUG - Created backup: D:\Amazon_Addon_For_Pinterest_Automation_v2\missing_products.bak.20250802_105743
2025-08-02 10:57:43,311 - amazon_affiliate_integration.processors.state_manager - DEBUG - Successfully wrote D:\Amazon_Addon_For_Pinterest_Automation_v2\missing_products.json
2025-08-02 10:57:43,311 - amazon_affiliate_integration - INFO - Phase 2: Cleaning up failed shortcodes...
2025-08-02 10:57:43,311 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress client initialized for cozytones.com (https://cozytones.com)
2025-08-02 10:57:43,312 - amazon_affiliate_integration.processors.cleanup_processor - INFO - Starting cleanup for 1 articles with visible shortcodes
2025-08-02 10:57:43,584 - httpcore.connection - DEBUG - connect_tcp.started host='cozytones.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 10:57:43,600 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000026683012CC0>
2025-08-02 10:57:43,600 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000026682FDCED0> server_hostname='cozytones.com' timeout=30.0
2025-08-02 10:57:43,616 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000266FFEABF20>
2025-08-02 10:57:43,617 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-02 10:57:43,617 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 10:57:43,618 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-02 10:57:43,618 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 10:57:43,618 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 10:57:44,711 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 04:57:44 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'Link', b'<https://cozytones.com/wp-json/>; rel="https://api.w.org/"'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'X-Wp-Total', b'1'), (b'X-Wp-Totalpages', b'1'), (b'Allow', b'GET, POST'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=aJHLU%2FZivVKsLKj5SEwe1d4pHDi8h03pbKUUBsbrLsxVFkTasaXgP3JIZe59OERTVpyyrFAFUfTUdGlpl7Br2mjUPq%2FArJ%2Bn%2Bmy1PzY%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'968aff000c1433bc-DAC')])
2025-08-02 10:57:44,712 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?slug=modern-farmhouse-bedroom-ideas&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-08-02 10:57:44,712 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-02 10:57:44,715 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 10:57:44,715 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 10:57:44,715 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 10:57:44,715 - httpcore.connection - DEBUG - close.started
2025-08-02 10:57:44,716 - httpcore.connection - DEBUG - close.complete
2025-08-02 10:57:44,716 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Created cleanup backup: cleanup_backup_304_20250802_105744.json
2025-08-02 10:57:44,718 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Article content length: 37858
2025-08-02 10:57:44,718 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Visible shortcodes to remove: 12
2025-08-02 10:57:44,718 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   1. Product: 'Reclaimed Wood Headboard' | Full: '[amazon bestseller=”Reclaimed Wood Headboard” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 10:57:44,718 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   2. Product: 'Westinghouse Iron Hill 1-Light Pendant Light' | Full: '[amazon bestseller=”Westinghouse Iron Hill 1-Light Pendant Light” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 10:57:44,718 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   3. Product: 'Linen Duvet Cover Set' | Full: '[amazon bestseller=”Linen Duvet Cover Set” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 10:57:44,718 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   4. Product: 'Vintage Distressed Nightstand' | Full: '[amazon bestseller=”Vintage Distressed Nightstand” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 10:57:44,718 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   5. Product: 'NuLOOM Moroccan Blythe Area Rug' | Full: '[amazon bestseller=”NuLOOM Moroccan Blythe Area Rug” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 10:57:44,718 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   6. Product: 'Reclaimed Wood Barn Door' | Full: '[amazon bestseller=”Reclaimed Wood Barn Door” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 10:57:44,718 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   7. Product: 'Modern Matte Black Metal Bed Frame' | Full: '[amazon bestseller=”Modern Matte Black Metal Bed Frame” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 10:57:44,718 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   8. Product: 'Vintage Mason Jar Pendant Light Fixtures' | Full: '[amazon bestseller=”Vintage Mason Jar Pendant Light Fixtures” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 10:57:44,719 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   9. Product: 'Linen Window Curtains' | Full: '[amazon bestseller=”Linen Window Curtains” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 10:57:44,719 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   10. Product: 'Linen Throw Pillow Set' | Full: '[amazon bestseller=”Linen Throw Pillow Set” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 10:57:44,719 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   11. Product: 'Vintage Botanical Framed Art Prints' | Full: '[amazon bestseller=”Vintage Botanical Framed Art Prints” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 10:57:44,719 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   12. Product: 'Wooden Clothing Rack with Distressed Finish' | Full: '[amazon bestseller=”Wooden Clothing Rack with Distressed Finish” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 10:57:44,719 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Starting shortcode removal from content (37858 chars)
2025-08-02 10:57:44,720 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Looking for 12 visible shortcodes
2025-08-02 10:57:44,720 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Wooden Clothing Rack with Distressed Finish'
2025-08-02 10:57:44,720 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 10:57:44,720 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Wooden Clothing Rack with Distressed Finish', trying shortcode-only pattern
2025-08-02 10:57:44,720 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 10:57:44,720 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Vintage Botanical Framed Art Prints'
2025-08-02 10:57:44,721 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 10:57:44,721 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Vintage Botanical Framed Art Prints', trying shortcode-only pattern
2025-08-02 10:57:44,721 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 10:57:44,721 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Linen Throw Pillow Set'
2025-08-02 10:57:44,721 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 10:57:44,721 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Linen Throw Pillow Set', trying shortcode-only pattern
2025-08-02 10:57:44,722 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 10:57:44,722 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Linen Window Curtains'
2025-08-02 10:57:44,722 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 10:57:44,722 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Linen Window Curtains', trying shortcode-only pattern
2025-08-02 10:57:44,722 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 10:57:44,722 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Vintage Mason Jar Pendant Light Fixtures'
2025-08-02 10:57:44,723 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 10:57:44,723 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Vintage Mason Jar Pendant Light Fixtures', trying shortcode-only pattern
2025-08-02 10:57:44,741 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 10:57:44,742 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Modern Matte Black Metal Bed Frame'
2025-08-02 10:57:44,742 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 10:57:44,742 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Modern Matte Black Metal Bed Frame', trying shortcode-only pattern
2025-08-02 10:57:44,742 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 10:57:44,742 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Reclaimed Wood Barn Door'
2025-08-02 10:57:44,743 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 10:57:44,743 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Reclaimed Wood Barn Door', trying shortcode-only pattern
2025-08-02 10:57:44,747 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 10:57:44,753 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'NuLOOM Moroccan Blythe Area Rug'
2025-08-02 10:57:44,753 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 10:57:44,753 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'NuLOOM Moroccan Blythe Area Rug', trying shortcode-only pattern
2025-08-02 10:57:44,753 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 10:57:44,754 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Vintage Distressed Nightstand'
2025-08-02 10:57:44,754 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 10:57:44,754 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Vintage Distressed Nightstand', trying shortcode-only pattern
2025-08-02 10:57:44,754 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 10:57:44,754 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Linen Duvet Cover Set'
2025-08-02 10:57:44,754 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 10:57:44,755 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Linen Duvet Cover Set', trying shortcode-only pattern
2025-08-02 10:57:44,755 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 10:57:44,755 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Westinghouse Iron Hill 1-Light Pendant Light'
2025-08-02 10:57:44,755 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 10:57:44,755 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Westinghouse Iron Hill 1-Light Pendant Light', trying shortcode-only pattern
2025-08-02 10:57:44,755 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 10:57:44,756 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Reclaimed Wood Headboard'
2025-08-02 10:57:44,756 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 10:57:44,756 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Reclaimed Wood Headboard', trying shortcode-only pattern
2025-08-02 10:57:44,756 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 10:57:44,756 - amazon_affiliate_integration.processors.cleanup_processor - INFO - No shortcodes to remove from https://cozytones.com/modern-farmhouse-bedroom-ideas/
2025-08-02 10:57:44,757 - amazon_affiliate_integration.processors.cleanup_processor - INFO - Cleaned up 1/1 articles (0 shortcodes removed)
2025-08-02 10:57:44,757 - amazon_affiliate_integration.processors.cleanup_processor - INFO - Cleanup completed: {'articles_processed': 1, 'shortcodes_removed': 0, 'cleanup_failures': 0, 'articles_updated': 1}
2025-08-02 10:57:44,757 - amazon_affiliate_integration - INFO - Crawl and cleanup workflow completed successfully in 5.23s
2025-08-02 10:57:44,757 - __main__ - INFO - Crawl and cleanup workflow completed successfully
