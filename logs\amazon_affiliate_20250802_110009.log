2025-08-02 11:00:09,815 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_110009.log
2025-08-02 11:00:09,816 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_110009.log
2025-08-02 11:00:09,816 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-08-02 11:00:09,816 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-08-02 11:00:09,816 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-08-02 11:00:09,827 - __main__ - INFO - Starting crawl-only operation...
2025-08-02 11:00:09,827 - amazon_affiliate_integration - INFO - Starting crawl and cleanup workflow...
2025-08-02 11:00:09,827 - amazon_affiliate_integration - INFO - Phase 1: Crawling articles for visible shortcodes...
2025-08-02 11:00:09,834 - amazon_affiliate_integration - INFO - Using browser crawler for cozytones.com
2025-08-02 11:00:10,346 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser initialized with 4 concurrent pages
2025-08-02 11:00:10,347 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Starting browser crawl of 1 articles...
2025-08-02 11:00:13,426 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Content snippet for shortcode detection: <!DOCTYPE html><html lang="en-US" data-scroll="0"><head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>12 Modern Farmhouse Bedroom Ideas for Rustic Elegance</title>
<meta name="robots" content="max-image-preview:large">
	<style>img:is([sizes="auto" i], [sizes^="auto," i]) { contain-intrinsic-size: 3000px 1500px }</style>
	<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link href="https://fonts.gstatic.com" crossorigin="" rel="preconnect">
<link rel="alternate" type="application/rss+xml" title="Cozy Tones » Feed" href="https://cozytones.com/feed/">
<link rel="alternate" type="application/rss+xml" title="Cozy Tones » Comments Feed" href="https://cozytones.com/comments/feed/">
<link rel="alternate" type="application/rss+xml" title="Cozy Tones » 12 Modern Farmhouse Bedroom Ideas for Rustic Elegance Comments Feed" href="https://cozytones.com/modern-farmhouse-bedroom-ideas/feed/">
<link rel="canonical" href="https://cozytones.com/modern-farmhouse-bedroom-ideas/">
<script>
window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/16.0.1\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/16.0.1\/svg\/","svgExt":".svg","source":{"concatemoji":"https:\/\/cozytones.com\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.2"}};
/*! This file is auto-generated */
!function(s,n){var o,i,e;function c(e){try{var t={supportTests:e,timestamp:(new Date).valueOf()};sessionStorage.setItem(o,JSON.stringify(t))}catch(e){}}function p(e,t,n){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);var t=new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data),a=(e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(n,0,0),new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data));return t.every(function(e,t){return e===a[t]})}function u(e,t){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);for(var n=e.getImageData(16,16,1,1),a=0;a
2025-08-02 11:00:13,427 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Content contains 12 '[amazon bestseller=' strings
2025-08-02 11:00:13,427 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Regex pattern found 12 matches in HTML
2025-08-02 11:00:13,429 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found 12 shortcodes in HTML, 12 in text, 12 unique
2025-08-02 11:00:13,430 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Reclaimed Wood Headboard' under heading: 'Recommended Products to replicate this idea' at position 26976
2025-08-02 11:00:13,430 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Westinghouse Iron Hill 1-Light Pendant Light' under heading: 'Recommended Products to replicate this idea' at position 30250
2025-08-02 11:00:13,430 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Linen Duvet Cover Set' under heading: 'Recommended Products to replicate this idea' at position 33371
2025-08-02 11:00:13,431 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Vintage Distressed Nightstand' under heading: 'Recommended Products to replicate this idea' at position 36362
2025-08-02 11:00:13,431 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'NuLOOM Moroccan Blythe Area Rug' under heading: 'Recommended Products to replicate this idea' at position 39351
2025-08-02 11:00:13,431 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Reclaimed Wood Barn Door' under heading: 'Recommended Products to replicate this idea' at position 42256
2025-08-02 11:00:13,431 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Modern Matte Black Metal Bed Frame' under heading: 'Recommended Products to replicate this idea' at position 45026
2025-08-02 11:00:13,432 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Vintage Mason Jar Pendant Light Fixtures' under heading: 'Recommended Products to replicate this idea' at position 47921
2025-08-02 11:00:13,432 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Linen Window Curtains' under heading: 'Recommended Products to replicate this idea' at position 50976
2025-08-02 11:00:13,432 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Linen Throw Pillow Set' under heading: 'Recommended Products to replicate this idea' at position 53789
2025-08-02 11:00:13,432 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Vintage Botanical Framed Art Prints' under heading: 'Recommended Products to replicate this idea' at position 56645
2025-08-02 11:00:13,432 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Wooden Clothing Rack with Distressed Finish' under heading: 'Recommended Products to replicate this idea' at position 59510
2025-08-02 11:00:13,438 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Progress: 1/1 articles crawled
2025-08-02 11:00:13,439 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser crawl completed: 1/1 successful in 3.09s (0.3 articles/sec)
2025-08-02 11:00:13,531 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser resources cleaned up
2025-08-02 11:00:13,531 - amazon_affiliate_integration - INFO - Crawled 1 articles for domain cozytones.com, found 12 visible shortcodes
2025-08-02 11:00:13,533 - amazon_affiliate_integration.processors.state_manager - DEBUG - Created backup: D:\Amazon_Addon_For_Pinterest_Automation_v2\missing_products.bak.20250802_110013
2025-08-02 11:00:13,535 - amazon_affiliate_integration.processors.state_manager - DEBUG - Successfully wrote D:\Amazon_Addon_For_Pinterest_Automation_v2\missing_products.json
2025-08-02 11:00:13,536 - amazon_affiliate_integration - INFO - Crawl and cleanup workflow completed successfully in 3.71s
2025-08-02 11:00:13,536 - __main__ - INFO - Crawl-only operation completed successfully
