2025-08-02 10:11:51,163 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_101151.log
2025-08-02 10:11:51,164 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_101151.log
2025-08-02 10:11:51,165 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-08-02 10:11:51,165 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-08-02 10:11:51,165 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-08-02 10:11:51,166 - __main__ - INFO - Starting complete workflow: Process → Crawl → Cleanup...
2025-08-02 10:11:51,166 - amazon_affiliate_integration - INFO - 🚀 Starting COMPLETE workflow: Process → Crawl → Cleanup
2025-08-02 10:11:51,166 - amazon_affiliate_integration - INFO - 📝 Phase 1: Processing articles to add Amazon shortcodes...
2025-08-02 10:11:51,166 - amazon_affiliate_integration - INFO - Starting domain processing: cozytones.com (force=False, dry_run=False, limit=1)
2025-08-02 10:11:51,166 - amazon_affiliate_integration - INFO - Loaded 0 previously processed URLs
2025-08-02 10:11:51,167 - amazon_affiliate_integration - INFO - Loaded 0 excluded URLs
2025-08-02 10:11:51,167 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress client initialized for cozytones.com (https://cozytones.com)
2025-08-02 10:11:52,313 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?per_page=1 "HTTP/1.1 200 OK"
2025-08-02 10:11:52,316 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress API connection test successful for cozytones.com
2025-08-02 10:11:52,316 - amazon_affiliate_integration - INFO - Fetching articles from cozytones.com...
2025-08-02 10:11:52,316 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetching articles from cozytones.com since 2025-01-01T00:00:00
2025-08-02 10:11:55,245 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?page=1&per_page=100&after=2025-01-01T00%3A00%3A00&status=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-08-02 10:11:56,747 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetched 100 articles from page 1
2025-08-02 10:11:56,747 - amazon_affiliate_integration.clients.wordpress_client - INFO - Limited to 1 articles for testing
2025-08-02 10:11:56,747 - amazon_affiliate_integration.clients.wordpress_client - INFO - Total articles fetched from cozytones.com: 1
2025-08-02 10:11:56,749 - amazon_affiliate_integration - INFO - Converted 1 articles to ArticleInfo objects
2025-08-02 10:11:56,750 - amazon_affiliate_integration - INFO - Normal mode: processing 1 articles (skipping 0 processed, 0 excluded)
2025-08-02 10:11:57,057 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Pre-processing checkpoint for cozytones.com (1 articles)
2025-08-02 10:11:57,057 - amazon_affiliate_integration - INFO - Created git checkpoint before processing cozytones.com
2025-08-02 10:11:57,058 - amazon_affiliate_integration - INFO - Processing 1 articles for cozytones.com...
2025-08-02 10:11:57,058 - amazon_affiliate_integration.processors.content_processor - INFO - Starting batch processing of 1 articles with concurrency limit 4
2025-08-02 10:11:57,058 - amazon_affiliate_integration.processors.content_processor - INFO - Processing article 304: 12 Modern Farmhouse Bedroom Ideas for Rustic Elegance
2025-08-02 10:11:57,061 - amazon_affiliate_integration.utils.backup_manager - INFO - Created backup: cozytones.com_304_pre_processing_20250802_101157.json
2025-08-02 10:11:57,062 - amazon_affiliate_integration.processors.content_processor - INFO - Generating products for 12 sections using batch processing
2025-08-02 10:11:57,063 - amazon_affiliate_integration.processors.content_processor - INFO - Using individual processing for reliable product generation
2025-08-02 10:11:59,087 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:11:59,094 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '1. Reclaimed Wood Headboard with Weathered Finish': ['Reclaimed Wood Headboard', 'Rustic Farmhouse Bedroom Set']
2025-08-02 10:12:01,547 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:12:01,549 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '2. Industrial-Style Lighting with Matte Black Fixtures': ['Industrial Matte Black Pendant Light Fixture', 'Matte Black Wall Sconce with Adjustable Arm']
2025-08-02 10:12:03,711 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:12:03,712 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '3. Linen Bedding in Soft Neutral Tones': ['Linen Duvet Cover Set', 'Luxury Neutral Tone Throw Blanket']
2025-08-02 10:12:05,644 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:12:05,650 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '4. Vintage-Inspired Nightstands with Distressed Paint': ['Vintage Distressed Wooden Nightstand', 'Hand-Painted Retro Nightstand with Chipped Pastel Finish']
2025-08-02 10:12:07,660 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:12:07,662 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '5. Neutral Area Rug with Subtle Pattern': ['Safavieh Adirondack Collection ADR109B Area Rug', 'nuLOOM Moroccan Blythe Area Rug']
2025-08-02 10:12:09,350 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:12:09,356 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '6. Classic Barn Door Sliding Closet Entrance': ['Reclaimed Wood Barn Door', 'Black Iron Hardware Sliding Door Kit']
2025-08-02 10:12:11,490 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:12:11,490 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '7. Minimalist Modern Bed Frame in Matte Finish': ['Minimalist Modern Bed Frame in Matte Finish', 'Queen Size Memory Foam Mattress']
2025-08-02 10:12:14,175 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:12:14,195 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '8. Mason Jar or Rustic Lanterns for Ambient Lighting': ['Vintage Mason Jar Pendant Lights', 'Rustic Metal Lanterns with Candle-Style LED Bulbs']
2025-08-02 10:12:16,078 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:12:16,093 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '9. Simple Linen-Covered Window Treatments': ['Linen Window Curtain Panels', 'Adjustable Curtain Rods']
2025-08-02 10:12:17,323 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:12:17,332 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '10. Layered Bedding with Cozy Throws and Pillows': ['Luxury Chunky Knit Throw Blanket', 'Set of Textured Linen and Velvet Throw Pillows']
2025-08-02 10:12:18,426 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:12:18,431 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '11. Framed Vintage Botanical Prints or Sketches': ['Vintage Botanical Wall Art Prints', 'Framed Botanical Illustration Prints']
2025-08-02 10:12:20,091 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:12:20,097 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '12. Practical yet Stylish Storage Solutions with Open Wardrobes': ['Wooden Clothing Rack with Distressed Finish', 'Open Wooden Shelving Unit for Closet Storage']
2025-08-02 10:12:20,201 - amazon_affiliate_integration.processors.content_processor - ERROR - Error in processing article 304 for 304: KeyError: '"level"'
2025-08-02 10:12:20,224 - amazon_affiliate_integration.processors.content_processor - INFO - Batch processing completed: 0 successful, 1 failed, 0 skipped
2025-08-02 10:12:20,224 - amazon_affiliate_integration - INFO - Updating WordPress content for cozytones.com...
2025-08-02 10:12:20,433 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Post-processing checkpoint for cozytones.com (0 articles processed)
2025-08-02 10:12:20,433 - amazon_affiliate_integration - INFO - Domain processing completed for cozytones.com: 0 processed, 1 failed, 0 skipped in 29.27s
2025-08-02 10:12:20,433 - amazon_affiliate_integration - INFO - ✅ Phase 1 completed successfully
2025-08-02 10:12:20,433 - amazon_affiliate_integration - INFO - 🕷️ Phase 2 & 3: Crawling and cleaning up failed shortcodes...
2025-08-02 10:12:20,433 - amazon_affiliate_integration - INFO - Starting crawl and cleanup workflow...
2025-08-02 10:12:20,434 - amazon_affiliate_integration - INFO - Phase 1: Crawling articles for visible shortcodes...
2025-08-02 10:12:20,434 - amazon_affiliate_integration - INFO - No processed articles found for domain cozytones.com
2025-08-02 10:12:20,434 - amazon_affiliate_integration - INFO - Phase 2: Cleaning up failed shortcodes...
2025-08-02 10:12:20,434 - amazon_affiliate_integration - INFO - Crawl and cleanup workflow completed successfully in 0.00s
2025-08-02 10:12:20,434 - amazon_affiliate_integration - INFO - ✅ Phase 2 & 3 completed successfully
2025-08-02 10:12:20,434 - amazon_affiliate_integration - INFO - 🎉 COMPLETE workflow finished successfully in 29.27s
2025-08-02 10:12:20,435 - __main__ - INFO - Complete workflow finished successfully
