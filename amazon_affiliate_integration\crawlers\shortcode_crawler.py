"""
Shortcode Crawler for Amazon Affiliate Integration

Crawls live WordPress articles to detect visible (failed) shortcodes and track
missing products for automated cleanup.
"""

import re
import asyncio
import aiohttp
import logging
from datetime import datetime
from typing import List, Dict, Optional, Set
from urllib.parse import urljoin, urlparse
import time

from ..core.models import (
    CrawlerResult, VisibleShortcode, CrawlerStatus, ArticleInfo
)
from ..core.config import CRAWLER_CONFIG


class ShortcodeCrawler:
    """
    Crawls live WordPress articles to detect visible shortcodes
    
    Features:
    - Async HTTP requests with configurable concurrency
    - Regex pattern matching for Amazon shortcodes
    - Rate limiting and retry mechanisms
    - Comprehensive error handling
    - Progress reporting
    """
    
    # Regex pattern to match Amazon shortcodes - handle all quote variations
    # Matches: right double quotation mark (") and double prime (″) - U+201D and U+2033
    SHORTCODE_PATTERN = re.compile(
        r'\[amazon bestseller=[\u201d\u2033]([^\u201d\u2033]+)[\u201d\u2033][^\]]*\]',
        re.IGNORECASE | re.MULTILINE
    )
    
    # Pattern to extract heading context (look for h3 before shortcode)
    HEADING_PATTERN = re.compile(
        r'<h3[^>]*class="wp-block-heading"[^>]*>([^<]+)</h3>',
        re.IGNORECASE | re.MULTILINE
    )
    
    def __init__(self, concurrency_limit: int = None, request_delay: float = None):
        """
        Initialize ShortcodeCrawler
        
        Args:
            concurrency_limit: Max concurrent requests (default from config)
            request_delay: Delay between requests in seconds (default from config)
        """
        self.concurrency_limit = concurrency_limit or CRAWLER_CONFIG['concurrency_limit']
        self.request_delay = request_delay or CRAWLER_CONFIG['request_delay']
        self.timeout = CRAWLER_CONFIG['timeout']
        self.max_retries = CRAWLER_CONFIG['max_retries']
        
        self.logger = logging.getLogger(__name__)
        self.session: Optional[aiohttp.ClientSession] = None
        self.semaphore: Optional[asyncio.Semaphore] = None
        
        # Statistics
        self.stats = {
            'total_crawled': 0,
            'successful_crawls': 0,
            'failed_crawls': 0,
            'visible_shortcodes_found': 0,
            'total_shortcodes_detected': 0
        }
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self._initialize_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self._cleanup_session()
    
    async def _initialize_session(self):
        """Initialize HTTP session and semaphore"""
        connector = aiohttp.TCPConnector(
            limit=self.concurrency_limit * 2,
            limit_per_host=self.concurrency_limit,
            ttl_dns_cache=300,
            use_dns_cache=True
        )
        
        timeout = aiohttp.ClientTimeout(total=self.timeout)
        
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=timeout,
            headers={
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            }
        )
        
        self.semaphore = asyncio.Semaphore(self.concurrency_limit)
        self.logger.info(f"Initialized crawler with {self.concurrency_limit} concurrent connections")
    
    async def _cleanup_session(self):
        """Clean up HTTP session"""
        if self.session:
            await self.session.close()
            self.session = None
        self.semaphore = None
    
    async def crawl_articles(self, articles: List[ArticleInfo]) -> List[CrawlerResult]:
        """
        Crawl multiple articles for visible shortcodes
        
        Args:
            articles: List of articles to crawl
            
        Returns:
            List of crawler results
        """
        if not articles:
            return []
        
        self.logger.info(f"Starting to crawl {len(articles)} articles for visible shortcodes")
        
        # Reset statistics
        self.stats = {key: 0 for key in self.stats}
        
        # Create crawling tasks
        tasks = [
            self._crawl_single_article(article)
            for article in articles
        ]
        
        # Execute with progress reporting
        results = []
        completed = 0
        
        for coro in asyncio.as_completed(tasks):
            try:
                result = await coro
                results.append(result)
                completed += 1
                
                # Update statistics
                self.stats['total_crawled'] += 1
                if result.success:
                    self.stats['successful_crawls'] += 1
                    self.stats['visible_shortcodes_found'] += len(result.visible_shortcodes)
                    self.stats['total_shortcodes_detected'] += result.total_shortcodes_found
                else:
                    self.stats['failed_crawls'] += 1
                
                # Progress reporting
                if completed % 10 == 0 or completed == len(articles):
                    self.logger.info(
                        f"Crawled {completed}/{len(articles)} articles "
                        f"({self.stats['visible_shortcodes_found']} visible shortcodes found)"
                    )
                    
            except Exception as e:
                self.logger.error(f"Unexpected error in crawling task: {e}")
                # Create failed result
                failed_result = CrawlerResult(
                    url="unknown",
                    domain="unknown", 
                    status=CrawlerStatus.FAILED,
                    error_message=str(e)
                )
                results.append(failed_result)
        
        self.logger.info(f"Crawling completed: {self.stats}")
        return results
    
    async def _crawl_single_article(self, article: ArticleInfo) -> CrawlerResult:
        """
        Crawl a single article for visible shortcodes
        
        Args:
            article: Article information
            
        Returns:
            Crawler result
        """
        start_time = time.time()
        
        async with self.semaphore:
            try:
                # Add delay between requests
                if self.request_delay > 0:
                    await asyncio.sleep(self.request_delay)
                
                # Attempt to fetch article content
                content = await self._fetch_article_content(article.url)
                
                if content is None:
                    return CrawlerResult(
                        url=article.url,
                        domain=article.domain,
                        status=CrawlerStatus.FAILED,
                        error_message="Failed to fetch article content",
                        response_time=time.time() - start_time
                    )
                
                # Detect visible shortcodes
                visible_shortcodes = self._detect_visible_shortcodes(content)
                
                return CrawlerResult(
                    url=article.url,
                    domain=article.domain,
                    status=CrawlerStatus.CRAWLED,
                    visible_shortcodes=visible_shortcodes,
                    total_shortcodes_found=len(visible_shortcodes),
                    response_time=time.time() - start_time
                )
                
            except Exception as e:
                self.logger.error(f"Error crawling {article.url}: {e}")
                return CrawlerResult(
                    url=article.url,
                    domain=article.domain,
                    status=CrawlerStatus.FAILED,
                    error_message=str(e),
                    response_time=time.time() - start_time
                )
    
    async def _fetch_article_content(self, url: str) -> Optional[str]:
        """
        Fetch article content with retry mechanism
        
        Args:
            url: Article URL to fetch
            
        Returns:
            Article HTML content or None if failed
        """
        for attempt in range(self.max_retries + 1):
            try:
                async with self.session.get(url) as response:
                    if response.status == 200:
                        content = await response.text()
                        self.logger.debug(f"Successfully fetched {url} (attempt {attempt + 1})")
                        return content
                    else:
                        self.logger.warning(f"HTTP {response.status} for {url} (attempt {attempt + 1})")
                        
            except asyncio.TimeoutError:
                self.logger.warning(f"Timeout fetching {url} (attempt {attempt + 1})")
            except Exception as e:
                self.logger.warning(f"Error fetching {url} (attempt {attempt + 1}): {e}")
            
            # Wait before retry (exponential backoff)
            if attempt < self.max_retries:
                wait_time = (2 ** attempt) * 0.5
                await asyncio.sleep(wait_time)
        
        self.logger.error(f"Failed to fetch {url} after {self.max_retries + 1} attempts")
        return None
    
    def _detect_visible_shortcodes(self, content: str) -> List[VisibleShortcode]:
        """
        Detect visible shortcodes in HTML content
        
        Args:
            content: HTML content to analyze
            
        Returns:
            List of visible shortcodes found
        """
        visible_shortcodes = []
        
        # Find all shortcode matches
        shortcode_matches = list(self.SHORTCODE_PATTERN.finditer(content))
        
        if not shortcode_matches:
            return visible_shortcodes
        
        # Find all heading matches for context
        heading_matches = list(self.HEADING_PATTERN.finditer(content))
        
        for match in shortcode_matches:
            product_name = match.group(1)
            full_shortcode = match.group(0)
            position = match.start()
            
            # Find the closest preceding heading
            heading = self._find_closest_heading(position, heading_matches)
            
            visible_shortcode = VisibleShortcode(
                product_name=product_name,
                heading=heading,
                full_shortcode=full_shortcode,
                position_in_content=position
            )
            
            visible_shortcodes.append(visible_shortcode)
            
            self.logger.debug(
                f"Found visible shortcode: '{product_name}' "
                f"under heading: '{heading}' at position {position}"
            )
        
        return visible_shortcodes
    
    def _find_closest_heading(self, shortcode_position: int, heading_matches: List) -> str:
        """
        Find the closest heading before a shortcode position
        
        Args:
            shortcode_position: Position of shortcode in content
            heading_matches: List of heading regex matches
            
        Returns:
            Heading text or default heading
        """
        closest_heading = "Recommended Products to replicate this idea"
        closest_distance = float('inf')
        
        for heading_match in heading_matches:
            heading_position = heading_match.start()
            
            # Only consider headings that come before the shortcode
            if heading_position < shortcode_position:
                distance = shortcode_position - heading_position
                if distance < closest_distance:
                    closest_distance = distance
                    closest_heading = heading_match.group(1).strip()
        
        return closest_heading
    
    def get_statistics(self) -> Dict:
        """Get crawler statistics"""
        return self.stats.copy()
