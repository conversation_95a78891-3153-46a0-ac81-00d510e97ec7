2025-08-02 11:10:07,827 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_111007.log
2025-08-02 11:10:07,828 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_111007.log
2025-08-02 11:10:07,828 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-08-02 11:10:07,828 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-08-02 11:10:07,828 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-08-02 11:10:07,829 - __main__ - INFO - Starting processing for domain: cozytones.com
2025-08-02 11:10:07,829 - amazon_affiliate_integration - INFO - Starting domain processing: cozytones.com (force=True, dry_run=False, limit=5)
2025-08-02 11:10:07,829 - amazon_affiliate_integration - INFO - Force mode enabled: ignoring previously processed URLs
2025-08-02 11:10:07,831 - amazon_affiliate_integration - INFO - Loaded 0 excluded URLs
2025-08-02 11:10:07,831 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress client initialized for cozytones.com (https://cozytones.com)
2025-08-02 11:10:08,228 - httpcore.connection - DEBUG - connect_tcp.started host='cozytones.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:08,249 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2229A2A0>
2025-08-02 11:10:08,249 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C222B0050> server_hostname='cozytones.com' timeout=30.0
2025-08-02 11:10:08,266 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C21B826F0>
2025-08-02 11:10:08,266 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-02 11:10:08,267 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:08,267 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-02 11:10:08,267 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:08,268 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 11:10:08,644 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:08 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'X-Wp-Total', b'139'), (b'X-Wp-Totalpages', b'139'), (b'Link', b'<https://cozytones.com/wp-json/wp/v2/posts?per_page=1&page=2>; rel="next"'), (b'Allow', b'GET, POST'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=cHf5Yx1sCPJjz7xgweaT7%2BFsXTvcS9Snwtx0q%2BZJgX6J0KMJfZ2Vu35GLccri1jprSgMi%2BpzSETjRqdhf%2BjP23prahTNXA%2BAf2To%2BfY%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'968b112e292bba50-DAC')])
2025-08-02 11:10:08,645 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?per_page=1 "HTTP/1.1 200 OK"
2025-08-02 11:10:08,645 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-02 11:10:08,646 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:08,647 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:08,647 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:08,647 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:08,648 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:08,648 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress API connection test successful for cozytones.com
2025-08-02 11:10:08,648 - amazon_affiliate_integration - INFO - Fetching articles from cozytones.com...
2025-08-02 11:10:08,648 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetching articles from cozytones.com since 2025-01-01T00:00:00
2025-08-02 11:10:08,875 - httpcore.connection - DEBUG - connect_tcp.started host='cozytones.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:08,886 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C218DBBC0>
2025-08-02 11:10:08,887 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C222B32D0> server_hostname='cozytones.com' timeout=30.0
2025-08-02 11:10:08,900 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2226E0F0>
2025-08-02 11:10:08,900 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-02 11:10:08,902 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:08,902 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-02 11:10:08,902 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:08,902 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 11:10:11,157 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:11 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'X-Wp-Total', b'139'), (b'X-Wp-Totalpages', b'2'), (b'Link', b'<https://cozytones.com/wp-json/wp/v2/posts?page=2&per_page=100&after=2025-01-01T00%3A00%3A00&status%5B0%5D=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit>; rel="next"'), (b'Allow', b'GET, POST'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=dyIn7uxXEd%2FNb9xb3xeePslL1zA96ofYSbb5YT4BnnlOBboF3K2Rnwv1KK4%2B8dT7xlo5bom1MbOx96OQnr3g8rP553ejow3K8WQsDtg%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'968b11322b8571f4-DAC')])
2025-08-02 11:10:11,158 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?page=1&per_page=100&after=2025-01-01T00%3A00%3A00&status=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-08-02 11:10:11,158 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-02 11:10:12,670 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:12,670 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:12,671 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:12,673 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:12,674 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:12,694 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetched 100 articles from page 1
2025-08-02 11:10:12,695 - amazon_affiliate_integration.clients.wordpress_client - INFO - Limited to 5 articles for testing
2025-08-02 11:10:12,695 - amazon_affiliate_integration.clients.wordpress_client - INFO - Total articles fetched from cozytones.com: 5
2025-08-02 11:10:12,697 - amazon_affiliate_integration.clients.wordpress_client - DEBUG - Converted 5 articles to ArticleInfo objects
2025-08-02 11:10:12,697 - amazon_affiliate_integration - INFO - Converted 5 articles to ArticleInfo objects
2025-08-02 11:10:12,698 - amazon_affiliate_integration - INFO - Force mode: processing 5 articles (excluding 0 excluded)
2025-08-02 11:10:12,912 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Pre-processing checkpoint for cozytones.com (5 articles)
2025-08-02 11:10:12,913 - amazon_affiliate_integration - INFO - Created git checkpoint before processing cozytones.com
2025-08-02 11:10:12,913 - amazon_affiliate_integration - INFO - Processing 5 articles for cozytones.com...
2025-08-02 11:10:12,913 - amazon_affiliate_integration.processors.content_processor - INFO - Starting batch processing of 5 articles with concurrency limit 4
2025-08-02 11:10:12,914 - amazon_affiliate_integration.processors.content_processor - INFO - Processing article 304: 12 Modern Farmhouse Bedroom Ideas for Rustic Elegance
2025-08-02 11:10:12,916 - amazon_affiliate_integration.processors.content_processor - INFO - Processing article 300: 11 Black and Pink Bedroom Ideas for Bold Contrast
2025-08-02 11:10:12,916 - amazon_affiliate_integration.processors.content_processor - INFO - Processing article 301: 14 Korean Bedroom Aesthetic Ideas for Minimalist Style
2025-08-02 11:10:12,917 - amazon_affiliate_integration.processors.content_processor - INFO - Processing article 302: 14 Pink and Blue Bedroom Ideas for Playful Charm
2025-08-02 11:10:12,941 - amazon_affiliate_integration.utils.backup_manager - INFO - Created backup: cozytones.com_304_pre_processing_20250802_111012.json
2025-08-02 11:10:12,942 - amazon_affiliate_integration.processors.content_processor - DEBUG - Parsed 0 H2 sections from content
2025-08-02 11:10:12,942 - amazon_affiliate_integration.processors.content_processor - WARNING - No H2 sections found in article 304
2025-08-02 11:10:12,942 - amazon_affiliate_integration.utils.backup_manager - INFO - Created backup: cozytones.com_300_pre_processing_20250802_111012.json
2025-08-02 11:10:12,943 - amazon_affiliate_integration.processors.content_processor - DEBUG - Parsed 11 H2 sections from content
2025-08-02 11:10:12,944 - amazon_affiliate_integration.processors.content_processor - INFO - Generating products for 11 sections using batch processing
2025-08-02 11:10:12,944 - amazon_affiliate_integration.processors.content_processor - INFO - Using individual processing for reliable product generation
2025-08-02 11:10:13,206 - amazon_affiliate_integration.utils.backup_manager - INFO - Created backup: cozytones.com_302_pre_processing_20250802_111012.json
2025-08-02 11:10:13,208 - amazon_affiliate_integration.processors.content_processor - DEBUG - Parsed 14 H2 sections from content
2025-08-02 11:10:13,208 - amazon_affiliate_integration.processors.content_processor - INFO - Generating products for 14 sections using batch processing
2025-08-02 11:10:13,208 - amazon_affiliate_integration.processors.content_processor - INFO - Using individual processing for reliable product generation
2025-08-02 11:10:13,208 - amazon_affiliate_integration.utils.backup_manager - INFO - Created backup: cozytones.com_301_pre_processing_20250802_111012.json
2025-08-02 11:10:13,210 - amazon_affiliate_integration.processors.content_processor - DEBUG - Parsed 14 H2 sections from content
2025-08-02 11:10:13,210 - amazon_affiliate_integration.processors.content_processor - INFO - Generating products for 14 sections using batch processing
2025-08-02 11:10:13,210 - amazon_affiliate_integration.processors.content_processor - INFO - Using individual processing for reliable product generation
2025-08-02 11:10:13,210 - amazon_affiliate_integration.processors.content_processor - INFO - Processing article 303: 17 Teen Bedroom Decor Ideas for Personal Style
2025-08-02 11:10:13,212 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:13,215 - amazon_affiliate_integration.utils.backup_manager - INFO - Created backup: cozytones.com_303_pre_processing_20250802_111013.json
2025-08-02 11:10:13,217 - amazon_affiliate_integration.processors.content_processor - DEBUG - Parsed 17 H2 sections from content
2025-08-02 11:10:13,217 - amazon_affiliate_integration.processors.content_processor - INFO - Generating products for 17 sections using batch processing
2025-08-02 11:10:13,218 - amazon_affiliate_integration.processors.content_processor - INFO - Using individual processing for reliable product generation
2025-08-02 11:10:13,236 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233E660>
2025-08-02 11:10:13,236 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C222B14D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:13,249 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233EAE0>
2025-08-02 11:10:13,250 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:13,250 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:13,250 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:13,252 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:13,252 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:14,239 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:14,240 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:14,240 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:14,241 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:14 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'349'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'433'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999519'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_0b5f68c3eba90d072efab78d5838b436'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=YznsqquN1Zd.v3l5jmsj3SMVNO6NvmHd0QmsVYKXxXI-1754111414-*******-Q6kV8NEGWRahB3WxDDHm68YnSpnOWIm1O67swjjJLAr1AA8SeuBmanq_7c9lIzh_WnpooFI1OyLuZ1h3jjc3_8Gc3JUJCziBGiqM2sS2lDQ; path=/; expires=Sat, 02-Aug-25 05:40:14 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=mt_IF2fEFKI3KCeXawX2i8ryz5R5PmoQ5Z5o7bhGjOQ-1754111414015-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b114d58b4ba56-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:14,242 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:14,242 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:14,243 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:14,243 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:14,244 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:14,244 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:14,245 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:14,245 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '1. Monochrome Black Walls with Bright Pink Accents': ['Hot Pink Table Lamp', 'Black Matte Finish Wallpaper']
2025-08-02 11:10:14,246 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '1. Monochrome Black Walls with Bright Pink Accents': ['Hot Pink Table Lamp', 'Black Matte Finish Wallpaper']
2025-08-02 11:10:14,251 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233DBB0>
2025-08-02 11:10:14,252 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C223298D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:14,252 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233D3D0>
2025-08-02 11:10:14,252 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232A3D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:14,253 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233CF50>
2025-08-02 11:10:14,253 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232ABD0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:14,265 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C220C6D50>
2025-08-02 11:10:14,266 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:14,272 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:14,272 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:14,272 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22317C20>
2025-08-02 11:10:14,273 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C220C6CF0>
2025-08-02 11:10:14,273 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:14,273 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:14,274 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:14,274 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:14,274 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:14,275 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:14,275 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:14,275 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:14,276 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:14,276 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:14,276 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:14,276 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:14,747 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:14,755 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22384950>
2025-08-02 11:10:14,755 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232B150> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:14,767 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22384860>
2025-08-02 11:10:14,768 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:14,768 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:14,769 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:14,769 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:14,769 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:14,833 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:14 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'221'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'268'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999536'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_057e916cf8e739f1773610117cd7b377'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=jDjVrB.MW3yOI1UGlXcpLA5g2qzvIzqTJGZx_ygp3Jc-1754111414-*******-cHh02.sPZihc7AtEeFVjGEiRbVcqCFSknzMgoJI9xBoCELTcqIuCryeIJkXRmkaFpZZYZ6GFRiPicQRHHHzhGft.kKSXDf8vTHSW0M2VuR0; path=/; expires=Sat, 02-Aug-25 05:40:14 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=kevyOxZKnKouYhdTKOtm9Q7y_hOp_Imhot6_BRDm4.Y-1754111414905-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b1153cc6f0645-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:14,834 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:14,834 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:14,835 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:14,835 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:14,836 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:14,836 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:14,836 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:14,837 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '1. Create a Gallery Wall with Trendy Prints and Quotes': ['Framed Wall Art Prints Set', 'Custom Motivational Quote Wall Canvas']
2025-08-02 11:10:14,837 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '1. Create a Gallery Wall with Trendy Prints and Quotes': ['Framed Wall Art Prints Set', 'Custom Motivational Quote Wall Canvas']
2025-08-02 11:10:15,261 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:15,274 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22385F10>
2025-08-02 11:10:15,275 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C22329950> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:15,291 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22385B50>
2025-08-02 11:10:15,291 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:15,292 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:15,292 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:15,292 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:15,293 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:15,312 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:15 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'242'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'256'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999512'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_b4bea0cee5932eb7d8949c2a78784b65'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=O9Zoydviuskk3FiqAx20TSNCRlcUpBJ02FqadqDwWSE-1754111415-*******-im.Np99ICvanzdyuGjk6bUXd03ir9f1yeaQry5wuTiVjf4bSPAt.W29QMzpYB8pueSitOmGTJLb6CJP3BfeqYXCn3_QpMzYmU8OKHE6v8OM; path=/; expires=Sat, 02-Aug-25 05:40:15 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=6o3cJ.MOIAlzBHTLEv4QUA1zKBxCfHjQmGnL6DXMnB4-1754111415385-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b1156cf59f257-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:15,313 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:15,315 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:15,315 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:15,316 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:15,316 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:15,316 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:15,317 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:15,317 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '2. Pink Upholstered Headboard with Black Bedding': ['Upholstered Pink Velvet Headboard', 'Black Satin Bed Sheets and Pillowcases Set']
2025-08-02 11:10:15,317 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '2. Pink Upholstered Headboard with Black Bedding': ['Upholstered Pink Velvet Headboard', 'Black Satin Bed Sheets and Pillowcases Set']
2025-08-02 11:10:15,756 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:15,756 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:15 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'673'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'704'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999525'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_2e2d34268faee14d18630078fafa8eb2'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=0YYHnewLEaYPQMu7SmFJlrx.OUUriBjjXpcQcp5aE98-1754111415-*******-Bf6.OV8dnOBdgMWG3bUvG6RFzZbRhLendFRU3dgBWDr6pIQ8vZXdmE40zANRfbt7NtC7Sq8E8z__KLB.vmtdkZQxbAd6LmlvFCckxsh5KvY; path=/; expires=Sat, 02-Aug-25 05:40:15 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=QZMeFTFwieRYtuh0o4fLQbE.k1SvHCnMdF_PWik.ai0-1754111415789-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b1153b97571f4-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:15,756 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:15,758 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:15,758 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:15 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'531'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'663'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999549'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_6809b13d4505173304cd9ecbfad2ac04'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=PBgyAUzPzl5_C0njE7lMUmVRbOQU7pnrOLkKd7vW.x4-1754111415-*******-MTbBoiS68juA6pcrVa2sVb21iWgos5VtniJPNg9UtAaYY3X5xOjGwC8SN0BhN0CYsV.sQ7rudgUkC2FDga7zhE4p1hgzYgJ2Nwd6uYxXddU; path=/; expires=Sat, 02-Aug-25 05:40:15 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=l1KgEyW.qviHlor8S0Sr5F6JL82YKOlZyQ1MLYwJeKo-1754111415816-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b1153be15ba59-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:15,759 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:15,759 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:15,759 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:15,759 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:15,759 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:15,760 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:15,760 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:15,761 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:15,761 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:15,761 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:15,762 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:15,762 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '1. Cozy Pink and Blue Bedding with Mix-and-Match Patterns': ['Queen-Size Pink Duvet Cover Set', 'Set of Assorted Blue and Blush Throw Pillows']
2025-08-02 11:10:15,762 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '1. Cozy Pink and Blue Bedding with Mix-and-Match Patterns': ['Queen-Size Pink Duvet Cover Set', 'Set of Assorted Blue and Blush Throw Pillows']
2025-08-02 11:10:15,762 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:15,763 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '1. Clean-lined Platform Bed with Neutral Bedding': ['West Elm Mid-Century Modern Low Profile Platform Bed', 'Parachute Linen Sheet Set in Neutral Tones']
2025-08-02 11:10:15,763 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '1. Clean-lined Platform Bed with Neutral Bedding': ['West Elm Mid-Century Modern Low Profile Platform Bed', 'Parachute Linen Sheet Set in Neutral Tones']
2025-08-02 11:10:15,769 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22317CB0>
2025-08-02 11:10:15,769 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232B550> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:15,781 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22317710>
2025-08-02 11:10:15,781 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:15,782 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:15,782 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:15,782 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:15,782 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:16,525 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:16,526 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:16,526 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:16 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'290'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'304'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999514'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_a612fe2fefab8d79eebd5321f0c88301'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=nd6xP2EoYuVzj7Kh768NPAcSXETeHxaxwyL.StWeu9g-1754111416-*******-6qaTdIjo.uKq5TmM7yMxw1mHcVw55q6Vpr9k9s.MQq1oOgJ0NGUSBwL3lLSYPXRM9f90gBDGpXcp4hV5z1oU88zALbpmXAOVo.aZU2._n.s; path=/; expires=Sat, 02-Aug-25 05:40:16 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=.GZigjsimMwZBIDyY1m8eX7W.WS49YsHIEcy7aPxKbE-1754111416450-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b115d2e612a54-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:16,527 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:16,528 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:16,528 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:16,528 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:16,529 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:16,529 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:16,530 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:16,530 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '3. Black and Pink Geometric Wall Art for Visual Interest': ['Large Black and Pink Geometric Wall Art Canvas Print', 'Custom Framed Geometric Pattern Wall Art']
2025-08-02 11:10:16,530 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '3. Black and Pink Geometric Wall Art for Visual Interest': ['Large Black and Pink Geometric Wall Art Canvas Print', 'Custom Framed Geometric Pattern Wall Art']
2025-08-02 11:10:16,539 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223876E0>
2025-08-02 11:10:16,540 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2239C4D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:16,540 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22387260>
2025-08-02 11:10:16,541 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C223602D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:16,551 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223872C0>
2025-08-02 11:10:16,555 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:16,555 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223846E0>
2025-08-02 11:10:16,556 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:16,556 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:16,556 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:16,557 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:16,557 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:16,557 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:16,557 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:16,558 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:16,558 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:16,797 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:16 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'654'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'782'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999540'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_0edc1bb43bb6e10fed7e6eee50c203ad'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=iN4JccOSHlPBv8TxKLCQs4Lln0mZDgWetPiuI3GjJeY-1754111416-*******-GVajMZfY4kc3uuAOhYup7RJacpaO.0iK8UV5hFt56e.f72GjxgPivMh22UMSVTJU93CVUlR5zw2nrnpnbqfMiS5v79YFdmUsli1ifAyqndg; path=/; expires=Sat, 02-Aug-25 05:40:16 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=nONPCn9WNiz.g7UAjidrn353Ax2EgVXrPi70tkn75w4-1754111416868-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b115a1a54ba5b-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:16,797 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:16,798 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:17,036 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:17,037 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:17,037 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:17,037 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:17,037 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:17,039 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:17,039 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '2. Incorporate Colorful Peel-and-Stick Wall Decals': ['WallDecal Warehouse Large Colorful Peel and Stick Wall Decals', 'RoomMates Neon Colorful Wall Decals Set']
2025-08-02 11:10:17,039 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '2. Incorporate Colorful Peel-and-Stick Wall Decals': ['WallDecal Warehouse Large Colorful Peel and Stick Wall Decals', 'RoomMates Neon Colorful Wall Decals Set']
2025-08-02 11:10:17,050 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233D7C0>
2025-08-02 11:10:17,050 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232BC50> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:17,064 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233E4E0>
2025-08-02 11:10:17,065 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:17,066 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:17,066 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:17,066 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:17,066 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:17,563 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:17,565 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:17 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'442'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'536'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999533'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_e516cf0b5c2f98a5f074acd62e8fb329'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=clhE9GVdrV2M5OFpWT.V4O88I.MkAP_ydP07DeuDDc0-1754111417-*******-ruXayqEKYfdbPuUq8SZ6cBwFJs1O1tBcROAGgsIhtJO.4aagOVvOgMZFNmTpTwPwrz_2Y7NL8qq3y03gvfwZNykJYHO4TaQp8oNA9w06r6A; path=/; expires=Sat, 02-Aug-25 05:40:17 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=nEyydXeU65Ta9H8BvWZkBWjUh7WjyutSXB1cYE8My4A-1754111417448-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b1161f8f30645-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:17,566 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:17,566 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:17,567 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:17,567 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:17,567 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:17,568 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:17,569 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:17,569 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '2. Floating Nightstands in Light Wood Finish': ['Floating Wooden Nightstand Shelf with LED Lighting', 'Minimalist Wall-Mounted Light Wood Nightstand']
2025-08-02 11:10:17,569 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '2. Floating Nightstands in Light Wood Finish': ['Floating Wooden Nightstand Shelf with LED Lighting', 'Minimalist Wall-Mounted Light Wood Nightstand']
2025-08-02 11:10:17,577 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223856A0>
2025-08-02 11:10:17,577 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C220F85D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:17,591 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22385130>
2025-08-02 11:10:17,591 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:17,592 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:17,592 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:17,592 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:17,592 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:17,596 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:17 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'660'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'715'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999530'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_fca5becde4bcc5b357de85b8437a58c6'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=J0eh9uzsYwS4FEypJoHar4NlDW5pX5AP70i4kf7j470-1754111417-*******-SP6pLkCgidWpy_gZhyGnOr_YoW7jq2_9IvHyHNiqTCIMzqsC5N24MZlPciehQ8iFSFqdeemw.XUJwwuVPER2kvUN.UmjYYClPn5OO0AHu8Q; path=/; expires=Sat, 02-Aug-25 05:40:17 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=DgGXrvqOq7HwT9l1gGFn9mEoDX3seioCv5o6inPctd0-1754111417668-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b1161f893ba59-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:17,596 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:17,597 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:17,600 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:17,600 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:17,600 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:17,600 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:17,601 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:17,601 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '2. Color-Coordinated Toy and Storage Bins in Pink and Blue': ['KidKraft Pastel Pink & Blue Fabric Toy Storage Bin Set', 'IRIS USA Pastel Pink & Blue Fabric Storage Bins with Labels']
2025-08-02 11:10:17,601 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '2. Color-Coordinated Toy and Storage Bins in Pink and Blue': ['KidKraft Pastel Pink & Blue Fabric Toy Storage Bin Set', 'IRIS USA Pastel Pink & Blue Fabric Storage Bins with Labels']
2025-08-02 11:10:17,689 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:17 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'332'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'348'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999511'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_2fb8b0f6d830b523747ed0848aa7c3ec'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=85051BeiFOtf702DxgSeSteF_bK7zPlMTLLmkz2RjhA-1754111417-*******-zUxSVkJFpVTOnZaZCPGf2NHUUTGYofs4W.fIahS5YFLfbJi4T7_SuUtunNjSfpI3uqWMk06_GutvjUZk.pUAP1Am3rmdiFxuZJzqPKndx7Y; path=/; expires=Sat, 02-Aug-25 05:40:17 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=kQIIAdk7R7FtGnVBaBAR6jWXOJyIqUdx9bsuRGR7iXs-1754111417762-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11652c73ded3-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:17,690 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:17,690 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:17,701 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:17,701 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:17,702 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:17,702 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:17,702 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:17,702 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '4. Pink Bed Frame with Black Bedding and Metallic Details': ['Upholstered Pink Bed Frame', 'Gold Metal Bed Frame Trim and Accents']
2025-08-02 11:10:17,702 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '4. Pink Bed Frame with Black Bedding and Metallic Details': ['Upholstered Pink Bed Frame', 'Gold Metal Bed Frame Trim and Accents']
2025-08-02 11:10:18,597 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:18,604 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:18,605 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:18,616 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A67E0>
2025-08-02 11:10:18,616 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C22329550> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:18,617 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C214A0BF0>
2025-08-02 11:10:18,617 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C22360450> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:18,618 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A51C0>
2025-08-02 11:10:18,618 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C22363E50> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:18,630 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A5280>
2025-08-02 11:10:18,630 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:18,634 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:18,635 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:18,635 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A7980>
2025-08-02 11:10:18,636 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A76B0>
2025-08-02 11:10:18,636 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:18,636 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:18,637 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:18,637 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:18,637 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:18,638 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:18,638 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:18,638 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:18,639 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:18,639 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:18,639 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:18,639 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:18,861 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:18 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'428'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'441'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999533'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_7050a95307fadebaf63ac0aac491f68d'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=tYFlDAwT_PVTEzcGoLAaaRpEMn7P74cR48cYZAvDPhE-1754111418-*******-wxg7VR.U3.L5xBEWHgn5yMhXht.2XdkE5o9BHNF0.mJAwz1yNs0Luevq36Rpcyt0sfb0qo6WquqzpF707_FJwsakOo.EhNYvXCPeSsS7xuM; path=/; expires=Sat, 02-Aug-25 05:40:18 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=QR5XbLDCym0bplFgrLsktVkNqxMxCcMVUJm3wPMA820-1754111418934-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11687f58ba56-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:18,862 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:18,862 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:18,869 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:18,869 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:18,870 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:18,870 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:18,870 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:18,871 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '3. Use Floating Shelves for Curated Decor and Accessories': ['Floating Wall Shelves Set of 2 or More', 'Minimalist Black or White Wall Mount Book Shelves']
2025-08-02 11:10:18,871 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '3. Use Floating Shelves for Curated Decor and Accessories': ['Floating Wall Shelves Set of 2 or More', 'Minimalist Black or White Wall Mount Book Shelves']
2025-08-02 11:10:19,253 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:19,261 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A49E0>
2025-08-02 11:10:19,261 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232A850> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:19,276 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A6A20>
2025-08-02 11:10:19,277 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:19,278 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:19,278 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:19,279 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:19,279 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:19,384 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:19 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'443'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'479'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999523'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_a20084f63869cf2cadd9a1581879441c'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=7Q8rsnph2RWw84lTkhe7g1a5HvpDz46J8I9eiDCm70I-1754111419-*******-2O0cEoDzdbmH4gVrdP071VhIQONi62qIBp6Ok1vnUq4tdUyvOpGbPyMeKxdlpdZM3smQWog0UzARCxhn4PAiz4jFNW3UNA0ATIJ8NMZX0I0; path=/; expires=Sat, 02-Aug-25 05:40:19 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=ibZ45MZt3w2bkDUDiP2ai3bnoz.DjKcLFtHAgce.z5E-1754111419457-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b116efe9aba50-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:19,385 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:19,385 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:19,390 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:19,390 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:19,391 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:19,391 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:19,391 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:19,392 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '3. DIY Pink and Blue Wall Art Using Canvas or Cardboard Cutouts': ['Large Gallery Wall Canvas Prints', 'Custom Framed Wall Art Prints']
2025-08-02 11:10:19,392 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '3. DIY Pink and Blue Wall Art Using Canvas or Cardboard Cutouts': ['Large Gallery Wall Canvas Prints', 'Custom Framed Wall Art Prints']
2025-08-02 11:10:19,722 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:19,733 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22385B20>
2025-08-02 11:10:19,734 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C22360550> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:19,747 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22385F10>
2025-08-02 11:10:19,747 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:19,748 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:19,748 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:19,748 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:19,749 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:19,749 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:19 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'383'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'412'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999518'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_8601e9df0cbbed48162bb44de36db6cc'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=60LCOXuljb1nNgLEzM9BMYRvpiazY5u3NEf0Ys11QLA-1754111419-*******-lHaRAFZEHPAlZQ.t2n52DQ4cdpt_6e_SRpSHsWuvPhYv6RmodDysw0nUkBgljrEEG93MgYB60Ec7ZkmysTTEVQ.tlV3nUg6FOFbPLQiG43I; path=/; expires=Sat, 02-Aug-25 05:40:19 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=PcuzLUY7s9QRHkILEyRKUu3HqRH0kjxEPHHOMwYLxtc-1754111419822-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b116efad271f4-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:19,750 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:19,750 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:19,761 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:19,761 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:19,762 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:19,762 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:19,763 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:19,763 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '5. Pink and Black Striped Bedding Sets for a Playful Look': ['Pink and Black Striped Bedding Set', 'Luxury Black and White Striped Comforter Set']
2025-08-02 11:10:19,763 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '5. Pink and Black Striped Bedding Sets for a Playful Look': ['Pink and Black Striped Bedding Set', 'Luxury Black and White Striped Comforter Set']
2025-08-02 11:10:20,297 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:20,298 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:20 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'885'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'916'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999524'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_f4df80063aea2306c2c3602ae12aa760'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=PaaNxstcN7.mxoz4m0_A5qQrcTbSSn3mj8IQLHUBPAU-1754111420-*******-_7iNB_wKqgDjwEnw4IqOX6lowGCl5Hlc4h9wkHpcLtTdayhEj_TJAFeoTHTKA9ruA.kudTwP_I8gBCgL7v.2pkeRiOTbGRT1ORUyi73.4rw; path=/; expires=Sat, 02-Aug-25 05:40:20 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=7I8pXtaDZ3QohOgA66YWjYiTToXR7fsrQ1IBQ1Kxr80-1754111420327-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b116efb20ba55-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:20,298 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:20,300 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:20,300 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:20,300 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:20,300 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:20,300 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:20,301 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:20,301 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '3. Monochrome Color Palette with Soft Pastels': ['Pastel Linen Throw Blanket', 'Set of Soft Pastel Cushion Covers']
2025-08-02 11:10:20,301 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '3. Monochrome Color Palette with Soft Pastels': ['Pastel Linen Throw Blanket', 'Set of Soft Pastel Cushion Covers']
2025-08-02 11:10:20,308 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C220C6BA0>
2025-08-02 11:10:20,309 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232A4D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:20,320 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C21B826F0>
2025-08-02 11:10:20,321 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:20,321 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:20,321 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:20,322 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:20,322 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:20,432 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:20 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'341'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'354'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999539'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_7bfa6e50d9fac67737bf1d05321335bd'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=clO5_o3cJq7Rg2ieYfrbcndkA_9t4Y.86IQOy.9Pptk-1754111420-*******-gNlKGyZDKdY0gqTTaqEfNs2xVwdjdL.jzabSYGzpCRXStdsVkOEfwuo.dEW8vw1Xj7tgP57XJOjgLheYBZyxTY3xltYyXhA0TxacaQTNzCo; path=/; expires=Sat, 02-Aug-25 05:40:20 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=Y_YNxhWM7tb0kXc.aNvapoO2gmu3GmViXbOq5t6Mcgg-1754111420504-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b1172fef05942-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:20,433 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:20,433 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:20,434 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:20,434 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:20,434 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:20,435 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:20,435 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:20,435 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '4. Hang String Lights for Cozy Ambiance': ['Globe String Lights for Indoor Decor', 'Vintage Edison Bulb String Lights']
2025-08-02 11:10:20,435 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '4. Hang String Lights for Cozy Ambiance': ['Globe String Lights for Indoor Decor', 'Vintage Edison Bulb String Lights']
2025-08-02 11:10:20,733 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:20,741 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233F4D0>
2025-08-02 11:10:20,741 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C223611D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:20,755 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233D490>
2025-08-02 11:10:20,756 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:20,756 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:20,756 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:20,757 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:20,757 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:20,964 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:21 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'465'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'491'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999534'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_dd8cdf3b74549ce032bb06338944870d'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=rfODaRSPY0AsFX4UWhopiT08RnvHSaDvCSmTb_sD.oo-1754111421-*******-OEpr_vqAMkpkRncAW6jkSPqUio2EkG4fzI_tGH.v0Ms6hhTdBZjEwCRtcQAvvY43_BcmStNpROG_idZCKfbKt9ZcqQDKbYe1pmuH87dev_Q; path=/; expires=Sat, 02-Aug-25 05:40:21 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=VB4qccHbp2mFcXWE.JStGdorxxsdGRfFLhBhdYC8TKg-1754111421037-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b1175ef7bba5f-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:20,965 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:20,965 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:20,966 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:20,966 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:20,966 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:20,967 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:20,967 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:20,967 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '4. Pink and Blue Themed Bedside Lamps with Whimsical Shades': ['Pink Cloud Bedside Lamp with Whimsical Shade', 'Blue Star Nightstand Lamp with Decorative Fringe']
2025-08-02 11:10:20,967 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '4. Pink and Blue Themed Bedside Lamps with Whimsical Shades': ['Pink Cloud Bedside Lamp with Whimsical Shade', 'Blue Star Nightstand Lamp with Decorative Fringe']
2025-08-02 11:10:20,970 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:21 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'362'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'390'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999510'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_dfbfb440f8d82f71bd1b9e2033e044dc'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=CDLJZq4aePBWOvHI5YxGbnyuSn82f_PRVZIMEeTdS9c-1754111421-*******-UlTIvbdKbdpnGYsrTRIpsSAUOM9R10410rJcJLJxBsLV62zityWldssQ3CQj4sz40obsTDp0g5LvOXku71NofxgzZwfU89aYLSz9Xx9yt00; path=/; expires=Sat, 02-Aug-25 05:40:21 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=w9VY4NtArJfievV0rBVxM.1d_uCowupMHxHuqLpImrE-1754111421043-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11798bb72a54-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:20,971 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:20,971 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:20,984 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:20,985 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:20,985 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:20,985 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:20,986 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:20,986 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '6. Accent Wall with Pink Wallpaper and Black Trim': ['Pink Textured Wallpaper', 'Black Decorative Wall Trim Molding']
2025-08-02 11:10:20,986 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '6. Accent Wall with Pink Wallpaper and Black Trim': ['Pink Textured Wallpaper', 'Black Decorative Wall Trim Molding']
2025-08-02 11:10:21,247 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:21,259 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233DBE0>
2025-08-02 11:10:21,260 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232AF50> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:21,274 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233ECF0>
2025-08-02 11:10:21,274 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:21,275 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:21,275 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:21,276 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:21,276 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:22,066 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:22,066 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:22,067 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:21 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'268'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'315'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999534'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_e67d15e21a61d80f384160bebcb06480'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=VelGm7OZrUVSUgDyhLBVe86EBkzCYA7YvMJitblfSVQ-1754111421-*******-b0Sh0BgYzGYykKz5P0x93Uxf0FTVzme1.XEu9ciDQHb.5YhTy5GEan.jN_VRDHVUdGgiRxxFgTxDDF8_V8PGmvljaTNRale_3kV4CzPz37k; path=/; expires=Sat, 02-Aug-25 05:40:21 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=uEdBfvdG.lH6rVX14gISygokyPhIJ4sJAGIT14fQLg4-1754111421832-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b117c397d33bc-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:22,068 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:22,068 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:22,069 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:22,069 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:22,069 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:22,069 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:22,070 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:22,070 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '4. Minimalist Window Treatments with Light Fabrics': ['Custom Sheer White Curtains', 'Motorized Light Filtering Window Shades']
2025-08-02 11:10:22,071 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '4. Minimalist Window Treatments with Light Fabrics': ['Custom Sheer White Curtains', 'Motorized Light Filtering Window Shades']
2025-08-02 11:10:22,080 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B6870>
2025-08-02 11:10:22,080 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C22329850> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:22,080 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B6C00>
2025-08-02 11:10:22,081 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C223298D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:22,095 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B5C40>
2025-08-02 11:10:22,095 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A69F0>
2025-08-02 11:10:22,095 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:22,096 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:22,096 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:22,096 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:22,096 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:22,097 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:22,097 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:22,097 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:22,097 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:22,097 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:22,570 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:22,578 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C21523F20>
2025-08-02 11:10:22,579 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232BB50> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:22,593 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C220007A0>
2025-08-02 11:10:22,594 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:22,594 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:22,595 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:22,595 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:22,595 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:22,677 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:22 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'278'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'294'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999516'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_a096d2375af88c3bc56ed5d608083df0'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=j7.ZQlQaw_bBr8muMMuA140PJvbuS8tlvf525z9Wh9o-1754111422-*******-2gYBPIkdgkmGW5HmLx6L9Lovf4rVkDiGtJC27MJH1HJ5Rd068mx.Nqzt.0ieh4gTPNLC4_aaVU0H36EVnc5dlbbbtyigRoewJmFY_6adfWE; path=/; expires=Sat, 02-Aug-25 05:40:22 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=l0jyIFUSgraWbtQCH_G1tG8eop5BqFS5fWvLhMWMuiQ-1754111422749-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11849e405942-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:22,678 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:22,678 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:22,679 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:22,679 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:22,679 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:22,680 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:22,680 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:22,680 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '7. Black Window Frames with Pink Curtain Panels': ['Black Window Frame with Glass', 'Pink Velvet Curtains with Curtain Rod']
2025-08-02 11:10:22,680 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '7. Black Window Frames with Pink Curtain Panels': ['Black Window Frame with Glass', 'Pink Velvet Curtains with Curtain Rod']
2025-08-02 11:10:22,687 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:22 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'573'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'707'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999528'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_fb2e52ed68c31f60ab5186c871c4be4f'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=m.GB4tXu9OR_aifThr3gYdZB6Y63cEaxSm3b81fXy4A-1754111422-*******-TK4G_.xv1XW4BkyIZQoZv0REE2ZinZ3PghM3APe8xyIyiv5WX2ETdx.EArTxU6fVLfarPViHTx0s5ENB.NOlkvdnSIp7KUPUy2Uf2YabjN8; path=/; expires=Sat, 02-Aug-25 05:40:22 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=znfoHE1HJpvat106p1pDLHZe72ge9QnXRDb9YkfaUJ0-1754111422759-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b117f783f814e-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:22,687 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:22,688 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:22,763 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:22,764 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:22,764 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:22,764 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:22,765 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:22,765 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '5. Personalize with DIY Canvas Art or Painted Signs': ['Framed Abstract Canvas Wall Art', 'Custom Motivational Quote Canvas Print']
2025-08-02 11:10:22,765 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '5. Personalize with DIY Canvas Art or Painted Signs': ['Framed Abstract Canvas Wall Art', 'Custom Motivational Quote Canvas Print']
2025-08-02 11:10:23,081 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:23,093 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22316930>
2025-08-02 11:10:23,093 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C22362BD0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:23,107 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223178C0>
2025-08-02 11:10:23,107 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:23,108 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:23,108 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:23,109 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:23,109 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:23,206 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:23 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'311'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'326'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999528'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_94ad06180ffae5f0f2c51ef80375eee6'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=VqH0ICvfbJePPfd3UDffD1EePf7Z1upWRgHffklo5v0-1754111423-*******-6d3QYPd24i3VGBN4EhrA2OijsdgwVWQz8ylzyhQgRJJ2YHQv1GKC3hDcZa594Z2BtpPfA4FojaBLL7Q0PTFOtaqXCmzQPariNCxz1EfMKjg; path=/; expires=Sat, 02-Aug-25 05:40:23 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=6VpfJK3hSgbz_FSTbzwKQbZhgmE4jwtBAbE3G7h_CNM-1754111423279-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11849e9771f4-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:23,207 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:23,208 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:23,208 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:23,208 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:23,209 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:23,209 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:23,209 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:23,210 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '5. Layered Pink and Blue Throw Pillows for a Cozy Bed Nook': ['Luxury Pink Velvet Throw Pillow', 'Quilted Blue Decorative Throw Pillow']
2025-08-02 11:10:23,210 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '5. Layered Pink and Blue Throw Pillows for a Cozy Bed Nook': ['Luxury Pink Velvet Throw Pillow', 'Quilted Blue Decorative Throw Pillow']
2025-08-02 11:10:23,839 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:23,839 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:23,840 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:23 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'386'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'400'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999533'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_a9d0ec95436c8f72eb1ada1c90fece28'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=8.e0yn6DzuHoVXTmgAxRPvjwq4bFjH2.2zOV1jkkbFw-1754111423-*******-cIiDz5gfGOYTUfT6l3TCOfzmWnUU.ToU3g2.mczlZEN8mgSwTv73nGtUTtfPxB9kwghm9F1ryk6FwoTeRbM6km4zxEJNUythBGIikgQCBAc; path=/; expires=Sat, 02-Aug-25 05:40:23 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=tjMnXhDknYO46yTOOjLzHVFMk.uXDMN5tMwUx9lnJ8o-1754111423794-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b1187b91fba4d-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:23,841 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:23,841 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:23,842 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:23 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'367'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'382'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999510'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_f96d4830ebba019274327f65eebac6e3'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=DAoWAIC_aVXOuoj9yWUxl1IRD4niyTdDtfkvegl56B0-1754111423-*******-UidAW9yRWYs3OD6P2rZEVUkKncgMkTShadIsY2.pfPd8SVRpz4jmJPbrLgzrr1AJ5QWTnIgiUuNrvI5yxXpA9vqQrUGQSgdIX2oFVuODMtk; path=/; expires=Sat, 02-Aug-25 05:40:23 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=07U9uTUq0dJR470cblCuVwoJTx4_MSFh1JDuj1WPsco-1754111423845-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b118aea77d058-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:23,842 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:23,843 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:23,843 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:23,843 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:23,843 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:23,843 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:23,845 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:23,845 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:23,845 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:23,846 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:23,846 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:23,846 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '8. Black Ceiling with Pink Pendant Lights or Chandeliers': ['Contemporary Pink Crystal Chandelier', 'Modern Matte Black Ceiling Mount with Pink Pendant Light']
2025-08-02 11:10:23,846 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '8. Black Ceiling with Pink Pendant Lights or Chandeliers': ['Contemporary Pink Crystal Chandelier', 'Modern Matte Black Ceiling Mount with Pink Pendant Light']
2025-08-02 11:10:23,847 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:23,848 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '5. Simple Geometric Wall Decor in Subtle Shades': ['3D Geometric Wall Panels', 'Modern Minimalist Wall Art Decor']
2025-08-02 11:10:23,848 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '5. Simple Geometric Wall Decor in Subtle Shades': ['3D Geometric Wall Panels', 'Modern Minimalist Wall Art Decor']
2025-08-02 11:10:23,855 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233E900>
2025-08-02 11:10:23,855 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C223606D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:23,855 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233C7D0>
2025-08-02 11:10:23,856 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C22360C50> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:23,871 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233F3B0>
2025-08-02 11:10:23,871 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233D880>
2025-08-02 11:10:23,872 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:23,873 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:23,873 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:23,873 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:23,874 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:23,874 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:23,874 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:23,874 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:23,874 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:23,875 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:24,570 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:24,570 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:24,572 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:24 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'343'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'355'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999529'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_e4744b72a3977678353a23e66b25656b'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=C.oKU21I29.3teKwwd7LmO_Mzwkqx.YKr_YD7zz4sAY-1754111424-*******-XfvSWNy3FcxXYwrHagiZT4pRJLSkiHHcFoXvVjU5Az0s151JG2nq2oM9lbu0tvLktEfxPwMJu3O6FNtJHohE7WA0JM.i424QO_sYtSelHvM; path=/; expires=Sat, 02-Aug-25 05:40:24 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=ouVfVT.zfQ284RMqYL0o6AgjPLYp92TI53tlJtaE0mw-1754111424604-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b118fb89871f4-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:24,572 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:24,573 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:24,573 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:24,574 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:24,574 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:24,574 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:24,575 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:24,576 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '6. Incorporate Multi-Functional Furniture with Hidden Storage': ['Storage Bed with Drawers', 'Multi-Functional Study Desk with Hidden Compartments']
2025-08-02 11:10:24,576 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '6. Incorporate Multi-Functional Furniture with Hidden Storage': ['Storage Bed with Drawers', 'Multi-Functional Study Desk with Hidden Compartments']
2025-08-02 11:10:24,584 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22385CA0>
2025-08-02 11:10:24,584 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232A3D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:24,585 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A7A40>
2025-08-02 11:10:24,586 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C220F85D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:24,598 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223876B0>
2025-08-02 11:10:24,598 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:24,601 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:24,602 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:24,602 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22385EB0>
2025-08-02 11:10:24,603 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:24,603 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:24,603 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:24,604 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:24,604 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:24,604 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:24,604 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:25,071 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:25,082 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A45F0>
2025-08-02 11:10:25,082 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C22329B50> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:25,094 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A4590>
2025-08-02 11:10:25,095 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:25,095 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:25,095 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:25,096 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:25,096 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:25,295 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:25 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'696'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'719'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999529'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_e1bfc6d596154f0a2d4b3af61190bd5f'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=rYDiWFHZDZRg.nvdM5o8YRWqtq5Bk816qgqRkOISzTI-1754111425-*******-G6oKU2f5MdqMKcEOHMb6eAgMR5Jy2w.YSwRhZN4GKL0eRa6M6eSjiJkIwiEo1qd234eichIaqDGih_6ikiWGoN.1fUrLIesugNIP8fGBbSU; path=/; expires=Sat, 02-Aug-25 05:40:25 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=MV.gy2FxuyhxL46HZ9Bjk5siiXdSkOlNDlD16qSdC4c-1754111425368-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b118fb8b1ba5f-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:25,296 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:25,296 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:25,302 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:25,302 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:25,302 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:25,303 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:25,303 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:25,303 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '6. Minimalist Pink and Blue Nightstand with Practical Storage': ['Mid-Century Modern Pink Nightstand with Storage Drawer', 'Minimalist Blue Table Lamp with USB Charging Port']
2025-08-02 11:10:25,303 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '6. Minimalist Pink and Blue Nightstand with Practical Storage': ['Mid-Century Modern Pink Nightstand with Storage Drawer', 'Minimalist Blue Table Lamp with USB Charging Port']
2025-08-02 11:10:25,643 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:25,654 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A6300>
2025-08-02 11:10:25,655 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232A7D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:25,663 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:25 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'278'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'293'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999541'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_5fc65d2c2f7a50a865564dbe4505514d'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=BRLBPyEEo3SDRfeeWWky_yp.7NLpI8rkmB.aQost4Go-1754111425-*******-3beGaxNKAmDcQed.GiFmSsurFYKwj_9MyYPrkEtupVuiW36yPJrB5VAgwXIrN5BIa9id8V55b6NTAATMGtLoYqpbMXDEpEfvn0.147dWYHk; path=/; expires=Sat, 02-Aug-25 05:40:25 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=GVqxUq2IalW9qZxTryDTMh8XIo_7SQpSGF78agQSZBI-1754111425737-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11975a3133bc-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:25,663 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:25,664 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:25,666 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A6990>
2025-08-02 11:10:25,667 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:25,667 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:25,668 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:25,668 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:25,668 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:25,669 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:25,669 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:25,670 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:25,670 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:25,670 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:25,671 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '7. Use Color-Themed Bedding and Pillow Arrangements': ['Luxury Egyptian Cotton Bed Sheets', 'High-Quality Designer Decorative Throw Pillows']
2025-08-02 11:10:25,671 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '7. Use Color-Themed Bedding and Pillow Arrangements': ['Luxury Egyptian Cotton Bed Sheets', 'High-Quality Designer Decorative Throw Pillows']
2025-08-02 11:10:25,676 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:25 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'303'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'315'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999531'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_ce5d3fe51a7d5bf76698d76f0f5f33c0'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=GrflbipfGCtAu5I7xdZL_9XukTx_A4wTRrrkX0IGb0o-1754111425-*******-bjMYMezAuL7LPiXvlDvKRTW83CldOt8veXTj37mt3EhYlJAbfDC4tR20MZdF5a3_zTFgll_blnbJjhqYGa6GpNubRCyBOzR8n2rn0beBgJg; path=/; expires=Sat, 02-Aug-25 05:40:25 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=knsafKNFccX.3wnERT1f3YSfg50w8ZecBkd0DWonN1s-1754111425748-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11944a21e87e-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:25,677 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:25,677 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:25,678 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:25,678 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:25,678 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:25,679 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:25,679 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:25,679 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '6. Compact Korean-inspired Wardrobe with Sliding Doors': ['Premium Korean-Style Sliding Wardrobe', 'Modular Wooden Closet Organizer System']
2025-08-02 11:10:25,679 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '6. Compact Korean-inspired Wardrobe with Sliding Doors': ['Premium Korean-Style Sliding Wardrobe', 'Modular Wooden Closet Organizer System']
2025-08-02 11:10:26,442 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:26,454 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:26,455 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:26 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'428'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'457'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999524'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_46ae2796cafe34a9002bd728a8c1e4fe'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=EAEdlJ4hTkN60DN5Z.RERMQssbHYaS2IDvmSo7BP9v8-1754111426-*******-VMupWsAMsC26.vMMUi3s9yRTromt5N8jLMigzsfg4vuxPZ2_RolkrAS1Uoo0c7LUKnoEW5CYIujMXlqIz69kmoJ12K.H19OSDADE2jH1SgY; path=/; expires=Sat, 02-Aug-25 05:40:26 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=eaUxqFFNj6r0IDl9.63eaIeNhP0ogqyyaV00kthY8X8-1754111426010-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11944d36ba59-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:26,455 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:26,456 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:26,456 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:26,456 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:26,457 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:26,457 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:26,459 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:26,460 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '9. Pink Bedding with Black Accent Pillows and Throws': ['Velvet Black Accent Throw Pillows', 'Satin Pink Comforter Cover Set']
2025-08-02 11:10:26,460 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '9. Pink Bedding with Black Accent Pillows and Throws': ['Velvet Black Accent Throw Pillows', 'Satin Pink Comforter Cover Set']
2025-08-02 11:10:26,465 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2205B3E0>
2025-08-02 11:10:26,466 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232BED0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:26,466 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2208DD90>
2025-08-02 11:10:26,466 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232B750> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:26,481 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C21F86300>
2025-08-02 11:10:26,483 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:26,486 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:26,486 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:26,487 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C21862480>
2025-08-02 11:10:26,487 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:26,487 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:26,487 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:26,488 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:26,488 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:26,488 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:26,488 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:26,512 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:26 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'412'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'425'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999535'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_0be90c915e2a463743278787ee37b9e0'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=In.VsZm0pQtVPj55l8p9Nn.kYzekQaxVc.kEIIgnM6Y-1754111426-*******-4DVaQO27WeAegE9Dc7eko1rX2ipUbzKdYhOtdtj2TWG7PrztfhugrIZbZ2jvFjPKHSUsKpGcj_DOpKRxYO53uLXgLVLgTUdPx33nQSY6Pqk; path=/; expires=Sat, 02-Aug-25 05:40:26 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=sWJycwNzFk6sE9j288Tsw9KiFT8vilavIybw0v20eZg-1754111426584-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b119aed9833bc-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:26,513 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:26,514 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:26,514 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:26,516 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:26,516 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:26,516 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:26,516 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:26,517 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '7. Fun Pink and Blue Wall Decals or Stickers for Instant Decor': ['Pink and Blue Wall Decal Stickers Set', 'Customizable Wall Sticker Art for Kids Room']
2025-08-02 11:10:26,517 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '7. Fun Pink and Blue Wall Decals or Stickers for Instant Decor': ['Pink and Blue Wall Decal Stickers Set', 'Customizable Wall Sticker Art for Kids Room']
2025-08-02 11:10:27,194 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:27,195 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:27,195 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:27 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'340'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'353'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999530'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_b6d9f3a685f94339a47e3a2f53c56b1c'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=wtZoC3u.nOXB6XfgyZyqrdOMT3O889cF5WtR8bVLi3Y-1754111427-*******-BEjGNXIeDNfvR_GSJJFBpHH4RB7vfltfh6_zAU_JRwwa3NsuNnq_l2s6qsiK_EHEwI.Gosop6AD.GJ.Fwru9iAkWX1jcK2YpRJzkvQXPvVM; path=/; expires=Sat, 02-Aug-25 05:40:27 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=RH0tBwBQAE9.eV0rSVy4dMa67j.h0ELFaOmVGmpJkT4-1754111427203-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11a00f5aba5c-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:27,196 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:27,196 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:27,197 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:27,197 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:27,197 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:27,198 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:27,199 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:27,199 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '8. Install a Chic Vanity or Mirror Corner for Glam Moments': ['Hollywood Vanity Mirror with Lights', 'Adjustable LED Ring Light for Makeup and Selfies']
2025-08-02 11:10:27,199 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '8. Install a Chic Vanity or Mirror Corner for Glam Moments': ['Hollywood Vanity Mirror with Lights', 'Adjustable LED Ring Light for Makeup and Selfies']
2025-08-02 11:10:27,208 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B7F80>
2025-08-02 11:10:27,208 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C223605D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:27,209 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B6A20>
2025-08-02 11:10:27,209 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C223636D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:27,222 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B7B00>
2025-08-02 11:10:27,223 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B5D90>
2025-08-02 11:10:27,223 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:27,224 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:27,224 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:27,224 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:27,225 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:27,225 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:27,226 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:27,226 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:27,226 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:27,226 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:27,445 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:27 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'250'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'263'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999538'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_0b6d9296f230c5b216d80a23927b04b3'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=4R5bfyBdEo7Q7fjKxz6vGgcvf8wkOEm67HjYMtklYE8-1754111427-*******-X2rdDv9nuh9b8riim8JxI0m8UWaJSqBGfly62XAgp8F1zFXE6p3n6hMbxU9rYwb191b_b3qwK3Zf.D0AQz_oUUvgbvBv51Ues5PnaWM6njo; path=/; expires=Sat, 02-Aug-25 05:40:27 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=PtJnrL.VQn_hCxlLPFjigOPKtuLKdOR2EseyvF1fWKU-1754111427518-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11a008350645-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:27,446 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:27,456 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:27,758 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:27,759 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:27,759 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:27,759 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:27,761 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:27,761 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:27,761 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '7. Tatami-style Floor Mats for Cozy Layers': ['Woven Straw Floor Mat', 'Natural Fiber Area Rug']
2025-08-02 11:10:27,761 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '7. Tatami-style Floor Mats for Cozy Layers': ['Woven Straw Floor Mat', 'Natural Fiber Area Rug']
2025-08-02 11:10:27,770 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A48C0>
2025-08-02 11:10:27,770 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C22363DD0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:27,783 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B7080>
2025-08-02 11:10:27,784 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:27,784 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:27,785 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:27,785 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:27,785 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:27,819 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:27 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'304'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'317'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999529'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_008e0cc296a46c6bd51de11ac91c8c46'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=A1Gh11CKxFcJVk8TJkrjhE4tfIs5vrQ_PUvNpLNbULE-1754111427-*******-_f6YhawkvFNKfj.OnoAHGe5rESiwjUTzEpoE3t58g7q2HmHXcijXtgWDu1ohALpkysBAdosREG2GogWdN2UiUCTTW7rSak1seU8Y6raNRrY; path=/; expires=Sat, 02-Aug-25 05:40:27 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=v9h7i_rT3HR4S5f4yCXjl_3FSIRNt4Pjii3DuXI54BM-1754111427892-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11a4aa7bded3-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:27,820 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:27,820 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:27,822 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:27,822 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:27,822 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:27,822 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:27,823 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:27,823 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '10. Black and Pink Art Prints in Modern Frames': ['Black and Pink Modern Framed Art Prints Set', 'Custom Framed Pink and Black Abstract Wall Art']
2025-08-02 11:10:27,823 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '10. Black and Pink Art Prints in Modern Frames': ['Black and Pink Modern Framed Art Prints Set', 'Custom Framed Pink and Black Abstract Wall Art']
2025-08-02 11:10:28,528 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:28,530 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:28,531 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:28 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'487'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'502'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999529'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_8bbcb24817a31720a60ce4812d814448'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=nNfJGw51hMBa2D2CEfCVHkQ9bTo_pBVvKKT8XEiN588-1754111428-*******-rgO.vtWBMp8NtinpZ1ldmXQ4WwfrDUmukWZQ.8eEq65cTb_b.0dUaU.5IOvfTlC6tLVLqgPoksGaMGjjTPA5KxclCLk7RldaChoKwguZ2Gw; path=/; expires=Sat, 02-Aug-25 05:40:28 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=LyBslEKPq0YoEhvphUI48q0d5P6GsCwp.89spXbA.mY-1754111428062-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11a4aa925942-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:28,532 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:28,532 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:28,533 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:28,533 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:28,534 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:28,536 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:28,536 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:28,537 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '8. Multi-Functional Pink and Blue Desk or Study Area': ['Pink and Blue Adjustable Desk Chair with Cushioned Seat', 'Multi-Compartment Wooden Desk with Drawers for Study Area']
2025-08-02 11:10:28,537 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '8. Multi-Functional Pink and Blue Desk or Study Area': ['Pink and Blue Adjustable Desk Chair with Cushioned Seat', 'Multi-Compartment Wooden Desk with Drawers for Study Area']
2025-08-02 11:10:28,544 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A5CA0>
2025-08-02 11:10:28,545 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C22360AD0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:28,545 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233EF60>
2025-08-02 11:10:28,545 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C22363E50> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:28,558 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A7020>
2025-08-02 11:10:28,558 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:28,562 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:28,563 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:28,563 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A4DA0>
2025-08-02 11:10:28,563 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:28,564 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:28,564 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:28,564 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:28,564 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:28,565 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:28,565 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:28,637 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:28 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'563'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'587'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999540'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_a261b5e41a8bd5923f1bd64ded8405c6'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=Vh7PICG.IdDx2lEYqRoJFl4S9kaycm..9EmwkNcqCtM-1754111428-*******-Xqs.RTJbxVpAACAOtLZeWOR_k2u1GY53z_T6WP1jnqhe7gooMriBqxflXvzyMkEbyX3.gSLngNaQmuIpdn9DMv0fOxGnho18LiGOZV_eRdw; path=/; expires=Sat, 02-Aug-25 05:40:28 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=fNSXJ.JCXko6hFwE2pv5gcDkeoeWETNUA.VSSTAux1k-1754111428711-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11a82e202a54-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:28,638 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:28,638 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:28,647 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:28,649 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:28,650 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:28,650 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:28,651 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:28,651 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '9. Display Personal Collections in Open-Style Shelving': ['Floating Wall Shelves Set of 3', 'Tempered Glass Display Cases for Collectibles']
2025-08-02 11:10:28,651 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '9. Display Personal Collections in Open-Style Shelving': ['Floating Wall Shelves Set of 3', 'Tempered Glass Display Cases for Collectibles']
2025-08-02 11:10:29,017 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:29,029 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233FF20>
2025-08-02 11:10:29,029 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C22360DD0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:29,040 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233FDA0>
2025-08-02 11:10:29,040 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:29,041 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:29,041 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:29,041 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:29,042 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:29,152 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:29 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'298'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'310'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999516'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_4097c6e9ad9fe33e5888164b300c560d'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=6KkpDZq99__Qe09Is.p5XqooDDNcg4WXQWr82JDNuOg-1754111429-*******-aS1waEreCCbYGM3LaaxQjLjuhIUOQCNr7Sk82MVacNIU8JKkNpZm5QRMaj3hyzWNZ5DNHwhaFe0i_IGM7VC6H2Ea8ZdG0AscXP46auZRtKk; path=/; expires=Sat, 02-Aug-25 05:40:29 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=FzU8tnFUBgEGSgqnAznQ8gRHBkF3QvVrHBepAMlI284-1754111429225-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11ad096971f4-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:29,153 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:29,154 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:29,154 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:29,154 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:29,155 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:29,155 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:29,155 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:29,156 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '11. Pink and Black Bedroom Makeover with Minimalist Approach': ['Black Metal Bed Frame', 'Pink Velvet Upholstered Bed Headboard']
2025-08-02 11:10:29,156 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '11. Pink and Black Bedroom Makeover with Minimalist Approach': ['Black Metal Bed Frame', 'Pink Velvet Upholstered Bed Headboard']
2025-08-02 11:10:29,547 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:29,548 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:29 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'469'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'492'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999539'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_aac65ccb0b52b3f7b58e0de9fceae99d'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=AOuuOWRx9PsDqK9Lx6W9bYeLf7QDPJeNHU.CTgjVkYA-1754111429-*******-pVwQ0gX5pX6SzC2rrIgyeQG9ShEkbzqZ_ZC3o5mRMxyOgDgqFgSsyYXbUaQZFcl0ygSfrQX9HltbSsbP7pG5ehKMQgaIz2L9MQ29NdUOHZU; path=/; expires=Sat, 02-Aug-25 05:40:29 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=WbIfSamQhQr209on.lyoCNAOqWS0URHR3FvuHIBFMRo-1754111429392-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11ad0ed0ba50-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:29,549 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:29,549 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:29,550 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '1. Monochrome Black Walls with Bright Pink Accents': ['Hot Pink Table Lamp', 'Black Matte Finish Wallpaper']
2025-08-02 11:10:29,550 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '2. Pink Upholstered Headboard with Black Bedding': ['Upholstered Pink Velvet Headboard', 'Black Satin Bed Sheets and Pillowcases Set']
2025-08-02 11:10:29,558 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '3. Black and Pink Geometric Wall Art for Visual Interest': ['Large Black and Pink Geometric Wall Art Canvas Print', 'Custom Framed Geometric Pattern Wall Art']
2025-08-02 11:10:29,559 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '4. Pink Bed Frame with Black Bedding and Metallic Details': ['Upholstered Pink Bed Frame', 'Gold Metal Bed Frame Trim and Accents']
2025-08-02 11:10:29,560 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '5. Pink and Black Striped Bedding Sets for a Playful Look': ['Pink and Black Striped Bedding Set', 'Luxury Black and White Striped Comforter Set']
2025-08-02 11:10:29,560 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '6. Accent Wall with Pink Wallpaper and Black Trim': ['Pink Textured Wallpaper', 'Black Decorative Wall Trim Molding']
2025-08-02 11:10:29,561 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '7. Black Window Frames with Pink Curtain Panels': ['Black Window Frame with Glass', 'Pink Velvet Curtains with Curtain Rod']
2025-08-02 11:10:29,561 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '8. Black Ceiling with Pink Pendant Lights or Chandeliers': ['Contemporary Pink Crystal Chandelier', 'Modern Matte Black Ceiling Mount with Pink Pendant Light']
2025-08-02 11:10:29,562 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '9. Pink Bedding with Black Accent Pillows and Throws': ['Velvet Black Accent Throw Pillows', 'Satin Pink Comforter Cover Set']
2025-08-02 11:10:29,563 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '10. Black and Pink Art Prints in Modern Frames': ['Black and Pink Modern Framed Art Prints Set', 'Custom Framed Pink and Black Abstract Wall Art']
2025-08-02 11:10:29,563 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '11. Pink and Black Bedroom Makeover with Minimalist Approach': ['Black Metal Bed Frame', 'Pink Velvet Upholstered Bed Headboard']
2025-08-02 11:10:29,563 - amazon_affiliate_integration.processors.content_processor - INFO - Inserted 22 product sections into content
2025-08-02 11:10:29,564 - amazon_affiliate_integration.processors.content_processor - INFO - Successfully processed article 300 in 16.65s. Added 22 shortcodes for 11 sections.
2025-08-02 11:10:29,564 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:29,564 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:29,565 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:29,565 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:29,566 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:29,566 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '8. Modular Open Shelving for Personal Items': ['Floating Wall Shelves - Set of 3', 'Mid-Century Modern Wooden Bookshelf Unit']
2025-08-02 11:10:29,566 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '8. Modular Open Shelving for Personal Items': ['Floating Wall Shelves - Set of 3', 'Mid-Century Modern Wooden Bookshelf Unit']
2025-08-02 11:10:29,576 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B72F0>
2025-08-02 11:10:29,577 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C22363F50> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:29,589 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B7080>
2025-08-02 11:10:29,590 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:29,591 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:29,591 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:29,591 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:29,591 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:30,052 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:30,053 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:29 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'527'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'551'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999519'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_c979250684d95ab49735411a40db89a1'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=rFAnAbGALfZaRMbLktl6eFRYDljhAySmdkUiQBzzAhY-1754111429-*******-nsyk3TWKeBCvv00FWCIBQMtyKa0WbzBHMVVUCKYgNYJubfGmv4DMAcPAr1Ns14h5zK4nDPtbP_RF8ioxKJh2FnABEwr8Oh2OIJTUSV9RgGE; path=/; expires=Sat, 02-Aug-25 05:40:29 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=4_PbD8PPQDOKV3dl2iB0X2lyfWp0TjGhcEFKcE6LtS4-1754111429980-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11b00ae92a54-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:30,053 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:30,054 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:30,054 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:30,054 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:30,055 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:30,055 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:30,056 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:30,056 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '9. Charming Pink and Blue Window Seat with Cushions and Storage': ['Upholstered Window Seat Bench with Storage', 'Decorative Linen and Velvet Cushion Set for Window Seat']
2025-08-02 11:10:30,056 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '9. Charming Pink and Blue Window Seat with Cushions and Storage': ['Upholstered Window Seat Bench with Storage', 'Decorative Linen and Velvet Cushion Set for Window Seat']
2025-08-02 11:10:30,064 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B66F0>
2025-08-02 11:10:30,065 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C222B0050> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:30,076 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B6150>
2025-08-02 11:10:30,076 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:30,077 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:30,077 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:30,077 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:30,077 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:30,097 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:30 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'233'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'248'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999538'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_cf2d3700093fe8e7700744c32b30a62e'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=RAkWkKn7ZLS8rzfizez.kk34_oJS24yf0df3XNt0oGY-1754111430-*******-JSciY6OVMaPu6a5ITXmqzTGdmau5Zj0UNowOXvXY1IVRHLJPTfi0DNrADAtZaN052BUj88bIJn2dhQAqJYxEN0_cIUXC3nJQV8uTf.Z2uP8; path=/; expires=Sat, 02-Aug-25 05:40:30 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=OxwcsH7gMRvPsdiKhtxruoijDAZ0QT8_q.UoJ_FOiFE-1754111430169-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11b37a1471f4-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:30,097 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:30,098 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:30,098 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:30,098 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:30,099 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:30,099 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:30,099 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:30,100 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '10. Incorporate Metallic or Glitter Accents for a Touch of Glam': ['Metallic Table Lamp with Gold Finish', 'Glitter Photo Frame with Silver Accents']
2025-08-02 11:10:30,100 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '10. Incorporate Metallic or Glitter Accents for a Touch of Glam': ['Metallic Table Lamp with Gold Finish', 'Glitter Photo Frame with Silver Accents']
2025-08-02 11:10:30,790 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:30,791 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:30,791 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:30 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'375'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'399'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999532'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_128c94fd9da5f67237248083c3502534'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=iYymJx1DQtc46JqncqyZKPJkEq510keQz8hb17yWeDk-1754111430-*******-G_l1WH2nSTGO9wUOBEc0Gy1DSUn2S4..VGQK.KywZGAFGiTPMygQa9uicuDJoSNSnCczhLrjNIdxtd1Yw7Kr.T_rx_WnVLJwrjsa7LD5X5c; path=/; expires=Sat, 02-Aug-25 05:40:30 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=9FUW7.X0e2wH2pPbWLcpSqwiycWy7vMeAWrbs9AnUMI-1754111430798-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11b67cbf5942-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:30,792 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:30,792 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:30,793 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:30,793 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:30,793 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:30,793 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:30,794 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:30,794 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '9. Multi-functional Furniture with Hidden Compartments': ['Modern Storage Ottoman with Hidden Compartment', 'Upholstered Bed Frame with Built-in Drawers']
2025-08-02 11:10:30,795 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '9. Multi-functional Furniture with Hidden Compartments': ['Modern Storage Ottoman with Hidden Compartment', 'Upholstered Bed Frame with Built-in Drawers']
2025-08-02 11:10:30,803 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233F3E0>
2025-08-02 11:10:30,803 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C222B1E50> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:30,803 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233D760>
2025-08-02 11:10:30,804 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C222B14D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:30,815 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B59A0>
2025-08-02 11:10:30,816 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:30,820 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:30,821 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:30,821 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B4380>
2025-08-02 11:10:30,821 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:30,821 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:30,822 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:30,822 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:30,822 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:30,823 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:30,823 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:31,284 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:31,295 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22316420>
2025-08-02 11:10:31,295 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232B950> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:31,306 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223164E0>
2025-08-02 11:10:31,307 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:31,308 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:31,308 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:31,308 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:31,308 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:31,580 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:31 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'370'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'497'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999535'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_7d2d3020215aa41b4c797518ef5b8d91'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=jk.Xy3tZ2bjxPBx9_FxHWi_kO8568JHwaeCx1n7qOjs-1754111431-*******-FUEnH39XoVIXhogU8rGvNCOBs0sVUWJQ50LhbtpDcOyvY7Sbjk1SlPHb_blGiX55SBnh0NKiPYXj1t7ACAT38vJodHn43bEuABPl8QfU9Ns; path=/; expires=Sat, 02-Aug-25 05:40:31 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=jl1LytI0nhz0nzhsaqcwo4ea5XIOLMRC4oI6WsQjEAo-1754111431652-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11bb2cdc33bc-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:31,581 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:31,581 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:31,585 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:31,585 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:31,585 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:31,585 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:31,586 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:31,586 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '11. Create a Cozy Reading Nook with Comfy Seating': ['Lazy Boy Reese Leather Recliner', 'Safavieh Adirondack Collection Vintage Blue Wool Rug']
2025-08-02 11:10:31,586 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '11. Create a Cozy Reading Nook with Comfy Seating': ['Lazy Boy Reese Leather Recliner', 'Safavieh Adirondack Collection Vintage Blue Wool Rug']
2025-08-02 11:10:31,904 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:31,906 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:31 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'606'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'637'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999524'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_65aa2119a2f5313028060d09d79bfdf1'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=hLmDJT5whwkRPP.iCSGvUCH.7pEtrTxFiB_DIyESsnc-1754111431-*******-ITYNjDCQOAP9uT23X2R9q2rhB0g76s9F._Mt4OMREqzdM1vNkj0zxWb.MjtQzjKiDa.3ydHD9WZBM6xPe4VkA69D989ch7nFtDWVt4e5Dio; path=/; expires=Sat, 02-Aug-25 05:40:31 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=IlZGSrxTd8fZi8g9UQBXj6HIRpTrh2wdZpee5I0u7LE-1754111431787-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11bb2ffdded3-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:31,906 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:31,907 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:31,907 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:31,908 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:31,908 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:31,908 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:31,909 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:31,909 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '10. Patterned Pink and Blue Wallpaper or Wall Covering for a Bold Look': ['Grasscloth Wallpaper Roll - Embossed Pink and Blue Textured Wall Covering', 'Custom Murals & Wall Coverings - Vibrant Abstract Pattern Wallpapers in Pink and Blue']
2025-08-02 11:10:31,910 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '10. Patterned Pink and Blue Wallpaper or Wall Covering for a Bold Look': ['Grasscloth Wallpaper Roll - Embossed Pink and Blue Textured Wall Covering', 'Custom Murals & Wall Coverings - Vibrant Abstract Pattern Wallpapers in Pink and Blue']
2025-08-02 11:10:31,919 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B5EE0>
2025-08-02 11:10:31,920 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C222B12D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:31,933 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B5D30>
2025-08-02 11:10:31,934 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:31,934 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:31,934 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:31,935 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:31,935 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:32,477 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:32,478 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:32 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'169'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'181'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999531'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_c8a20d741b80709f65ec00baf2bbf764'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=nnORlxCsGDmWVVI8NAcmfzSAr4NRIdfEKRPmR3huwEs-1754111432-*******-ufD1WZGu5K.bvOm02CeJnjIh0t4sNKnjK1e16ciQmAJGoQ9X_NohvMSskjwGhfMbyGaMXrsX09nqb2mJhgR_vfyaZef1RhP8aDkPaq_7svQ; path=/; expires=Sat, 02-Aug-25 05:40:32 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=VNro42DZyR7vG2xHYfWj0h9nd5ZkTYDAw6tp3NsMD6s-1754111432338-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11be2eb2814e-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:32,479 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:32,479 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:32,480 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:32,480 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:32,480 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:32,481 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:32,481 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:32,481 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '10. Minimalist Desk or Vanity with Clean Lines': ['Minimalist Wooden Desk', 'Adjustable Modern Office Stool']
2025-08-02 11:10:32,482 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '10. Minimalist Desk or Vanity with Clean Lines': ['Minimalist Wooden Desk', 'Adjustable Modern Office Stool']
2025-08-02 11:10:32,491 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A5DF0>
2025-08-02 11:10:32,491 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C220F8CD0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:32,503 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A74A0>
2025-08-02 11:10:32,504 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:32,504 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:32,504 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:32,505 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:32,505 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:32,569 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:32 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'321'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'349'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999533'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_c09d81cba5fe5490bbfb9e0e3ade9d8c'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=8UA5k0mccHYkh8Ur9OMnCr.MhwvGsmXJSeGKL6N3AKA-1754111432-*******-Pc99tZlIuoXToKoMysHCjA5r3YI.Q2Ocm51FOYDszckGf..DdcTW1mg7MiXIORDemTZdGmn4OF247tpGv556TQlpjPSeQrAJjjqKyJgOdwE; path=/; expires=Sat, 02-Aug-25 05:40:32 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=h2A0_8I6TmnypL2.83ntgfcW1OriBKzmudwUy7z4SK0-1754111432642-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11c21d7fba5f-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:32,570 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:32,571 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:32,576 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:32,576 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:32,577 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:32,577 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:32,577 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:32,578 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '12. Hang Inspirational Motto Banners or Wall Art': ['Inspirational Wall Art Canvas Prints', 'Custom Fabric Banner with Uplifting Quote']
2025-08-02 11:10:32,578 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '12. Hang Inspirational Motto Banners or Wall Art': ['Inspirational Wall Art Canvas Prints', 'Custom Fabric Banner with Uplifting Quote']
2025-08-02 11:10:33,281 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:33,281 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:33,282 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:33 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'244'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'297'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999526'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_c684f2e727d59f64564afb448279e05b'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=E2ytcabwvVJe6uao8P.kvGNPNYD3U70NbKT5wQmSwOE-1754111433-*******-KfqFC_5qiPWRozlJRV_UnuPtUaRjYG.KZ7v4pmsOqr_kBF1w56RkDcpzR0fMFZJ52PEur.3uAbVgt4GQN24X3TEwUpgZmSH1DaiBwtKrkzk; path=/; expires=Sat, 02-Aug-25 05:40:33 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=4ziD4YpxKiDaZTBsf84Egdn6d2OzimPIgA0OI_JY0Y0-1754111433150-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11c5aa382a54-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:33,283 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:33,283 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:33,284 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:33,284 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:33,284 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:33,284 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:33,285 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:33,285 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '11. DIY Pink and Blue String Light Garland for a Festive Glow': ['String Fairy Lights for Bedroom', 'LED Pink and Blue Lantern String Lights']
2025-08-02 11:10:33,286 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '11. DIY Pink and Blue String Light Garland for a Festive Glow': ['String Fairy Lights for Bedroom', 'LED Pink and Blue Lantern String Lights']
2025-08-02 11:10:33,295 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22384230>
2025-08-02 11:10:33,296 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232B4D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:33,296 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223841A0>
2025-08-02 11:10:33,297 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C22329950> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:33,310 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22384530>
2025-08-02 11:10:33,310 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C22385820>
2025-08-02 11:10:33,310 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:33,311 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:33,311 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:33,311 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:33,312 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:33,312 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:33,312 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:33,312 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:33,313 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:33,313 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:33,726 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:33,747 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C220C6BD0>
2025-08-02 11:10:33,747 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232AE50> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:33,761 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C1EBCE270>
2025-08-02 11:10:33,761 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:33,762 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:33,762 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:33,763 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:33,763 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:33,959 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:34 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'361'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'382'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999535'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_c97b229df41defaea813e98176210bec'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=ebbOKHMTj7GClbQ5.A8JjjlWuMPoQCnzhwgwdI1fWwY-1754111434-*******-zuXHokvsHr_EZhSeRADCfNVrr5oPsKcQpaWu9IEEaxXLo73ma50nmVq1Zn1qCVWM1i04pa61JvHFAnIvYzDZwvKe0KD8ruZPwslz5Id_UlA; path=/; expires=Sat, 02-Aug-25 05:40:34 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=Bsmi9u0.aJpXZW_OHpuXtVUUT8BJp1MjoGMokwed_0Q-1754111434031-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11cabaf971f4-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:33,959 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:33,960 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:33,965 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:33,965 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:33,967 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:33,967 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:33,968 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:33,968 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '13. Add a DIY Headboard Using Upholstered Fabric or Fairy Lights': ['Upholstered Fabric Headboard', 'LED Fairy Lights String Set']
2025-08-02 11:10:33,968 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '13. Add a DIY Headboard Using Upholstered Fabric or Fairy Lights': ['Upholstered Fabric Headboard', 'LED Fairy Lights String Set']
2025-08-02 11:10:33,972 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:34 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'367'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'396'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4998'), (b'x-ratelimit-remaining-tokens', b'3999538'), (b'x-ratelimit-reset-requests', b'17ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_1791c14190c31561f6112de8af5a7b60'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=rBPqscA_mWjzcYA1t7F21Hlp0rsJ2ddyr6qFYs6t7PQ-1754111434-*******-A8yIPLZKzL2HoOVPRGdV.qtr2QsciPI4bWFq1wjvbGr7RJywER5o3GG.M26xeyBujgURWCbsgFJWMlIy76nCLVotRa96yDlr8EWrcK5uwuM; path=/; expires=Sat, 02-Aug-25 05:40:34 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=dTMkB6GjvWp.S3KQ1fHKvAnOklxyTYZOGH1blnYdVLg-1754111434045-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11cabd482a54-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:33,973 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:33,973 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:33,983 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:33,984 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:33,984 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:33,984 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:33,985 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:33,985 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '11. Under-bed Storage to Maximize Space Efficiency': ['Under Bed Storage Drawer Set', 'Flat Under Bed Storage Bags']
2025-08-02 11:10:33,985 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '11. Under-bed Storage to Maximize Space Efficiency': ['Under Bed Storage Drawer Set', 'Flat Under Bed Storage Bags']
2025-08-02 11:10:34,299 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:34,311 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A6570>
2025-08-02 11:10:34,311 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C222B1E50> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:34,327 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A4140>
2025-08-02 11:10:34,327 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:34,328 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:34,346 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:34,348 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:34,360 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:34,804 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:34,804 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:34 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'555'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'576'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999530'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_d38bfe9c26bfabc60b6ee166a1308693'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=dupxcwI_cVDk_xyL0e4371rN388FXxCOlJIPDWvlNNc-1754111434-*******-ptd.C0hKpNjhl8QDDIjkmBWTHZ73KySozeYDoedsHske3GgmWtEaDkdBBOiC52YN6SgTDWmRs13Xc0TDlc.R.0Mdcy8aVZwJ7OaJ0n6v_1A; path=/; expires=Sat, 02-Aug-25 05:40:34 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=WLZy.uXSlVNuB9VEHvwKV3xtun7Bp1GKckBtUjYnAH8-1754111434691-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11cd88960645-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:34,805 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:34,805 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:34,805 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:34,806 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:34,806 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:34,806 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:34,807 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:34,807 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '12. Colorful Pink and Blue Storage Ottomans or Poufs': ['Pink Tufted Storage Ottoman', 'Blue Upholstered Storage Pouf']
2025-08-02 11:10:34,807 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '12. Colorful Pink and Blue Storage Ottomans or Poufs': ['Pink Tufted Storage Ottoman', 'Blue Upholstered Storage Pouf']
2025-08-02 11:10:34,815 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A4410>
2025-08-02 11:10:34,815 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C22329550> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:34,826 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A5F40>
2025-08-02 11:10:34,827 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:34,827 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:34,827 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:34,828 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:34,828 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:35,058 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:35 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'411'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'421'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999545'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_fa211f39e6e0a061562df57a604c530f'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=9PCZ6i3taGKcSnAZw6mfzB0VWzdCph1eSjSZrjljfds-1754111435-*******-wg3KomZt0Ov6VcWNzeO.WM2cw1l1UDA7RTBhOWcS.2Vu_wC7l12Jkd3EsTXLB46ShGSS6Vg6bc4Y_y5LQyrPWhluodRv2Db7ozb_7ChFV2w; path=/; expires=Sat, 02-Aug-25 05:40:35 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=4xpT406E_hO5JvfC1FbWkBdmZ5c05aMQJ2pHpnIPbq4-1754111435131-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11d13d9fba53-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:35,059 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:35,059 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:35,062 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:35,062 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:35,063 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:35,063 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:35,063 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:35,063 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '14. Use Colorful Storage Bins and Organizers for Practicality': ['Stackable Plastic Storage Bins with Lids', 'Multi-Compartment Desk Organizer Bliers']
2025-08-02 11:10:35,063 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '14. Use Colorful Storage Bins and Organizers for Practicality': ['Stackable Plastic Storage Bins with Lids', 'Multi-Compartment Desk Organizer Bliers']
2025-08-02 11:10:35,339 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:35,351 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233DE50>
2025-08-02 11:10:35,352 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232B950> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:35,363 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233FDD0>
2025-08-02 11:10:35,363 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:35,364 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:35,364 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:35,364 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:35,364 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:35,860 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:35,861 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:35 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'472'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'502'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999545'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_8711dbe8aa0ee3e84d9ea307e996b2df'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=xddW6LXAV4CrlcuwpXV5cKCgIuTNDAeSkQS45s3qpi4-1754111435-*******-0WS0DHtIJHPVMmDzYo_Cq.kSaFa5KICkcqS8noQBtkgmxXpkDXb2GWE0BhH0SK2LwUeOKqZgIuUbJBo81SAY8IKHzweaeZP4ufKVqXpa63A; path=/; expires=Sat, 02-Aug-25 05:40:35 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=kpnB2muHFU7LkHQfH2xVbKkqUNyTjbTsiFVqODxJt1w-1754111435685-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11d42881ba5c-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:35,862 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:35,863 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:35,863 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:35,863 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:35,864 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:35,864 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:35,865 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:35,865 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '12. Natural Materials and Textures for Warmth': ['Linen Duvet Cover Set', 'Handcrafted Wooden Nightstand']
2025-08-02 11:10:35,865 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '12. Natural Materials and Textures for Warmth': ['Linen Duvet Cover Set', 'Handcrafted Wooden Nightstand']
2025-08-02 11:10:35,871 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A4CB0>
2025-08-02 11:10:35,872 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C223298D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:35,885 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A5B50>
2025-08-02 11:10:35,886 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:35,886 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:35,887 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:35,887 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:35,887 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:36,373 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:36,381 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B4770>
2025-08-02 11:10:36,381 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232A450> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:36,393 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B4DA0>
2025-08-02 11:10:36,393 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:36,394 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:36,394 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:36,394 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:36,394 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:36,491 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:36 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'300'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'321'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999533'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_06177002da924f9b590d040b9dc7dbff'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=u6dVl9fXe8YAT0vnBcyTWsfkIkRYiB18zEJwLlLTXxo-1754111436-*******-9ebZn9MSY1wuyazV8h0DyqhUIeK0Xyw40VU5I4xUqRu8ar2vtqzpVx70yBt2OcOzPVHIlRIEYkzXH57_WOKrzlo7bqt6hS0l7AVS.RF8BRo; path=/; expires=Sat, 02-Aug-25 05:40:36 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=QGJg0F84U9VmZ_K_LNrkFaT74KUcXe60B3CrFV_YgJM-1754111436564-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11d78916ded3-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:36,492 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:36,492 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:36,497 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:36,497 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:36,498 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:36,498 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:36,498 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:36,499 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '13. Themed Pink and Blue Curtains with Fun Motifs (e.g., animals, stars)': ['Pink Star Pattern Window Curtain Panels', "Blue Animal Print Children's Room Curtains"]
2025-08-02 11:10:36,499 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '13. Themed Pink and Blue Curtains with Fun Motifs (e.g., animals, stars)': ['Pink Star Pattern Window Curtain Panels', "Blue Animal Print Children's Room Curtains"]
2025-08-02 11:10:36,515 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:36 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'313'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'334'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999541'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_212373da32d2d3e2df17af0ac6128edf'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=u3RgB2pRBjMgsXwfQYR0P7I5psDR_6lPiWQx.d3R0v4-1754111436-*******-fIxeiLNRAspu11MGy.ovuZ9OzakwRBDfkmTB5L03JRQIgT9sbVqtvLS21XusW62xlO67s_L6WVno4saFZuln1Ej1OifgujgJ5o9U_ldFGgE; path=/; expires=Sat, 02-Aug-25 05:40:36 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=3dLdJVxodo.WRQrtOt5uFbOLfiYisWeZ8DwXaEh5hWA-1754111436588-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11dace49ba56-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:36,516 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:36,516 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:36,520 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:36,520 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:36,521 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:36,521 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:36,522 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:36,522 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '15. Incorporate a Chalkboard or Whiteboard Wall for Creativity': ['Whiteboard Wall Paint Kit', 'Commercial Chalkboard Wall Panel System']
2025-08-02 11:10:36,522 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '15. Incorporate a Chalkboard or Whiteboard Wall for Creativity': ['Whiteboard Wall Paint Kit', 'Commercial Chalkboard Wall Panel System']
2025-08-02 11:10:36,911 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:36,919 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B7680>
2025-08-02 11:10:36,919 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232A3D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:36,931 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B6180>
2025-08-02 11:10:36,931 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:36,932 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:36,932 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:36,932 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:36,932 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:37,343 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:37,356 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B6090>
2025-08-02 11:10:37,356 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C222B14D0> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:37,369 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B6B40>
2025-08-02 11:10:37,369 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:37,370 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:37,370 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:37,370 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:37,370 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:37,664 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:37 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'480'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'495'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999552'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_fb5977a5f388de454e0d8ad3ec39b6bd'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=yRe2d0eeHboEBIPNn4Zy8CA6gnbJ2w7COvEYOPmV8HA-1754111437-*******-SVmkcVOE_sWhrPAOhO8iEe97rYY45ffESux4HfMn.6Xjkud.E9juu98Un491m6Mi5lnPWxHF4geHHdv8YJe1Ik24j4d.Jz9hq3HhXRreDxw; path=/; expires=Sat, 02-Aug-25 05:40:37 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=9gHl2_.AniMlIeS_uROJ6vxebdQVGSoItZWqWmZBzWk-1754111437736-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11ddfd93ded3-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:37,664 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:37,664 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:37,667 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:37,667 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:37,667 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:37,668 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:37,668 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:37,668 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '13. Subdued, Elegant Bedding in Solid Colors': ['Egyptian Cotton Sateen Bed Sheets', 'Down Alternative Duvet Insert']
2025-08-02 11:10:37,668 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '13. Subdued, Elegant Bedding in Solid Colors': ['Egyptian Cotton Sateen Bed Sheets', 'Down Alternative Duvet Insert']
2025-08-02 11:10:37,745 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:37 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'511'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'540'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999520'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_150c7c4fdad7ec4204e8cc3b8f4aed18'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=1bvz93MiPPwoGMHpn7OtDLQEN1v1o7X1lJnjRwHvK.0-1754111437-*******-PAzRoiXeUdNJlmW3ddS9j9tXyV5lPAQ5c4vOaGIjgCq99Cbs2I5UIKmM.yppaBzY8BijEyXDdzyM74B0pIgJPma5SigItXvm5whiUrK3yrM; path=/; expires=Sat, 02-Aug-25 05:40:37 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=yDZ1cRv5NFYPvWPg8uSPrUCawxRwU1hmohorkIkdldo-1754111437818-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11e15dbbf257-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:37,746 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:37,746 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:37,749 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:37,750 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:37,750 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:37,750 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:37,750 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:37,750 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '14. Practical Pink and Blue Clothes Closet or Wardrobe with Organized Compartments': ['Custom Closet Organizer System', 'Premium Wooden Wardrobe with Adjustable Shelves']
2025-08-02 11:10:37,751 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '14. Practical Pink and Blue Clothes Closet or Wardrobe with Organized Compartments': ['Custom Closet Organizer System', 'Premium Wooden Wardrobe with Adjustable Shelves']
2025-08-02 11:10:38,051 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:38,052 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:37 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'189'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'202'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999539'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_0e909d4ac84a51f30718e6753983b5cf'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=XcB1.CxIbQCfkARG541u_dVdehdhoan6Zx45S1I.ae4-1754111437-*******-PNuWe4lgLV_NJ0E_CfCnfjzEspm1bVFMAtzdMq8V9_z8XVVvYKJ8KsoQmeL2sFmBghOBbbFhXKIO2Bh.m5skzZ68axB55UcHlvAcwRe_r4M; path=/; expires=Sat, 02-Aug-25 05:40:37 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=shVfNgPnAwpUIKbWE9CP9hLFxjXsnW6wEFTYjhcvnqI-1754111437926-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11e41f82e87e-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:38,053 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:38,053 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:38,054 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:38,054 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:38,054 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '1. Cozy Pink and Blue Bedding with Mix-and-Match Patterns': ['Queen-Size Pink Duvet Cover Set', 'Set of Assorted Blue and Blush Throw Pillows']
2025-08-02 11:10:38,063 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '2. Color-Coordinated Toy and Storage Bins in Pink and Blue': ['KidKraft Pastel Pink & Blue Fabric Toy Storage Bin Set', 'IRIS USA Pastel Pink & Blue Fabric Storage Bins with Labels']
2025-08-02 11:10:38,063 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '3. DIY Pink and Blue Wall Art Using Canvas or Cardboard Cutouts': ['Large Gallery Wall Canvas Prints', 'Custom Framed Wall Art Prints']
2025-08-02 11:10:38,064 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '4. Pink and Blue Themed Bedside Lamps with Whimsical Shades': ['Pink Cloud Bedside Lamp with Whimsical Shade', 'Blue Star Nightstand Lamp with Decorative Fringe']
2025-08-02 11:10:38,064 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '5. Layered Pink and Blue Throw Pillows for a Cozy Bed Nook': ['Luxury Pink Velvet Throw Pillow', 'Quilted Blue Decorative Throw Pillow']
2025-08-02 11:10:38,065 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '6. Minimalist Pink and Blue Nightstand with Practical Storage': ['Mid-Century Modern Pink Nightstand with Storage Drawer', 'Minimalist Blue Table Lamp with USB Charging Port']
2025-08-02 11:10:38,065 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '7. Fun Pink and Blue Wall Decals or Stickers for Instant Decor': ['Pink and Blue Wall Decal Stickers Set', 'Customizable Wall Sticker Art for Kids Room']
2025-08-02 11:10:38,066 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '8. Multi-Functional Pink and Blue Desk or Study Area': ['Pink and Blue Adjustable Desk Chair with Cushioned Seat', 'Multi-Compartment Wooden Desk with Drawers for Study Area']
2025-08-02 11:10:38,066 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '9. Charming Pink and Blue Window Seat with Cushions and Storage': ['Upholstered Window Seat Bench with Storage', 'Decorative Linen and Velvet Cushion Set for Window Seat']
2025-08-02 11:10:38,067 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '10. Patterned Pink and Blue Wallpaper or Wall Covering for a Bold Look': ['Grasscloth Wallpaper Roll - Embossed Pink and Blue Textured Wall Covering', 'Custom Murals & Wall Coverings - Vibrant Abstract Pattern Wallpapers in Pink and Blue']
2025-08-02 11:10:38,068 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '11. DIY Pink and Blue String Light Garland for a Festive Glow': ['String Fairy Lights for Bedroom', 'LED Pink and Blue Lantern String Lights']
2025-08-02 11:10:38,068 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '12. Colorful Pink and Blue Storage Ottomans or Poufs': ['Pink Tufted Storage Ottoman', 'Blue Upholstered Storage Pouf']
2025-08-02 11:10:38,069 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '13. Themed Pink and Blue Curtains with Fun Motifs (e.g., animals, stars)': ['Pink Star Pattern Window Curtain Panels', "Blue Animal Print Children's Room Curtains"]
2025-08-02 11:10:38,069 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '14. Practical Pink and Blue Clothes Closet or Wardrobe with Organized Compartments': ['Custom Closet Organizer System', 'Premium Wooden Wardrobe with Adjustable Shelves']
2025-08-02 11:10:38,070 - amazon_affiliate_integration.processors.content_processor - INFO - Inserted 28 product sections into content
2025-08-02 11:10:38,070 - amazon_affiliate_integration.processors.content_processor - INFO - Successfully processed article 302 in 25.15s. Added 28 shortcodes for 14 sections.
2025-08-02 11:10:38,070 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:38,070 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:38,071 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:38,071 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '16. Decorate with Trendy Tapestry or Textile Wall Hangings': ['Large Woven Tapestry Wall Hanging', 'Bohemian Textile Wall Art Decor']
2025-08-02 11:10:38,071 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '16. Decorate with Trendy Tapestry or Textile Wall Hangings': ['Large Woven Tapestry Wall Hanging', 'Bohemian Textile Wall Art Decor']
2025-08-02 11:10:38,081 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B4C20>
2025-08-02 11:10:38,081 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232A850> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:38,095 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B7950>
2025-08-02 11:10:38,096 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:38,096 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:38,097 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:38,097 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:38,097 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:38,533 - httpcore.connection - DEBUG - connect_tcp.started host='api.openai.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:38,541 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B6CC0>
2025-08-02 11:10:38,542 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C222B1E50> server_hostname='api.openai.com' timeout=30.0
2025-08-02 11:10:38,554 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223B5F10>
2025-08-02 11:10:38,556 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:38,556 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:38,556 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:38,557 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:38,557 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:38,765 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:38 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'386'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'408'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999528'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'7ms'), (b'x-request-id', b'req_0c84082ca159125bf855381376de05fd'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=8MVMLXcMlwct6F0NGkTEcrYxWOhFE2mj2RzHWXkyqog-1754111438-*******-N_t2oxCH5Nks7BXfDRH3xSTSBlJDNSCH1_yGPi28nZv9ewhWUD1aO31hGAxjOoQvs50Ak2gmcjcD5Xu1yuDCyxcYFEBT4QPpMDIUGzDrnyU; path=/; expires=Sat, 02-Aug-25 05:40:38 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=Pc8fkE.xRCV_6lT.z.dcaXhD3WrIKhlGGjRc0fqiq4s-1754111438838-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11e89df0ba50-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:38,766 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:38,766 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:38,768 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:38,768 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:38,769 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:38,769 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:38,769 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:38,769 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '14. Discreet, Minimalist Decor Accessories': ['Minimalist Ceramic Tray', 'Modern Wall Clock']
2025-08-02 11:10:38,769 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '14. Discreet, Minimalist Decor Accessories': ['Minimalist Ceramic Tray', 'Modern Wall Clock']
2025-08-02 11:10:38,876 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '1. Clean-lined Platform Bed with Neutral Bedding': ['West Elm Mid-Century Modern Low Profile Platform Bed', 'Parachute Linen Sheet Set in Neutral Tones']
2025-08-02 11:10:38,877 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '2. Floating Nightstands in Light Wood Finish': ['Floating Wooden Nightstand Shelf with LED Lighting', 'Minimalist Wall-Mounted Light Wood Nightstand']
2025-08-02 11:10:38,878 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '3. Monochrome Color Palette with Soft Pastels': ['Pastel Linen Throw Blanket', 'Set of Soft Pastel Cushion Covers']
2025-08-02 11:10:38,878 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '4. Minimalist Window Treatments with Light Fabrics': ['Custom Sheer White Curtains', 'Motorized Light Filtering Window Shades']
2025-08-02 11:10:38,879 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '5. Simple Geometric Wall Decor in Subtle Shades': ['3D Geometric Wall Panels', 'Modern Minimalist Wall Art Decor']
2025-08-02 11:10:38,880 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '6. Compact Korean-inspired Wardrobe with Sliding Doors': ['Premium Korean-Style Sliding Wardrobe', 'Modular Wooden Closet Organizer System']
2025-08-02 11:10:38,880 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '7. Tatami-style Floor Mats for Cozy Layers': ['Woven Straw Floor Mat', 'Natural Fiber Area Rug']
2025-08-02 11:10:38,880 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '8. Modular Open Shelving for Personal Items': ['Floating Wall Shelves - Set of 3', 'Mid-Century Modern Wooden Bookshelf Unit']
2025-08-02 11:10:38,881 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '9. Multi-functional Furniture with Hidden Compartments': ['Modern Storage Ottoman with Hidden Compartment', 'Upholstered Bed Frame with Built-in Drawers']
2025-08-02 11:10:38,881 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '10. Minimalist Desk or Vanity with Clean Lines': ['Minimalist Wooden Desk', 'Adjustable Modern Office Stool']
2025-08-02 11:10:38,882 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '11. Under-bed Storage to Maximize Space Efficiency': ['Under Bed Storage Drawer Set', 'Flat Under Bed Storage Bags']
2025-08-02 11:10:38,882 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '12. Natural Materials and Textures for Warmth': ['Linen Duvet Cover Set', 'Handcrafted Wooden Nightstand']
2025-08-02 11:10:38,882 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '13. Subdued, Elegant Bedding in Solid Colors': ['Egyptian Cotton Sateen Bed Sheets', 'Down Alternative Duvet Insert']
2025-08-02 11:10:38,884 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '14. Discreet, Minimalist Decor Accessories': ['Minimalist Ceramic Tray', 'Modern Wall Clock']
2025-08-02 11:10:38,884 - amazon_affiliate_integration.processors.content_processor - INFO - Inserted 28 product sections into content
2025-08-02 11:10:38,884 - amazon_affiliate_integration.processors.content_processor - INFO - Successfully processed article 301 in 25.97s. Added 28 shortcodes for 14 sections.
2025-08-02 11:10:39,320 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:39 GMT'), (b'Content-Type', b'application/json'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'access-control-expose-headers', b'X-Request-ID'), (b'openai-organization', b'rankzenith'), (b'openai-processing-ms', b'430'), (b'openai-project', b'proj_hBCwi0DUFzXwbYJ3ayW0YVin'), (b'openai-version', b'2020-10-01'), (b'x-envoy-upstream-service-time', b'444'), (b'x-ratelimit-limit-requests', b'5000'), (b'x-ratelimit-limit-tokens', b'4000000'), (b'x-ratelimit-remaining-requests', b'4999'), (b'x-ratelimit-remaining-tokens', b'3999540'), (b'x-ratelimit-reset-requests', b'12ms'), (b'x-ratelimit-reset-tokens', b'6ms'), (b'x-request-id', b'req_2325454772955722ad09e0e82ec5fef0'), (b'x-envoy-decorator-operation', b'router.openai.svc.cluster.local:5004/*'), (b'strict-transport-security', b'max-age=31536000; includeSubDomains; preload'), (b'cf-cache-status', b'DYNAMIC'), (b'Set-Cookie', b'__cf_bm=m1IGHufmwc3SuSiaZpqO6Ic59DpKpup0pYivgT9v.W8-1754111439-*******-KGHPEp2sXPo0wPIvtmSnj0T8TDOwW267Pyu.IwxB45Pr9Ql9O_nbUoc8pHAX0CaRYfy6YbgT5fzGu98uGySj9Z8ZbBZ8BKEFHbn_TQ0SCHU; path=/; expires=Sat, 02-Aug-25 05:40:39 GMT; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'X-Content-Type-Options', b'nosniff'), (b'Set-Cookie', b'_cfuvid=QP6zvSb1TFw8pdAIw.QXMOX1eSwxs_Ttgz5n8ERnnEg-1754111439392-*******-*********; path=/; domain=.api.openai.com; HttpOnly; Secure; SameSite=None'), (b'Server', b'cloudflare'), (b'CF-RAY', b'968b11eb7dfbba56-DAC'), (b'Content-Encoding', b'gzip'), (b'alt-svc', b'h3=":443"; ma=86400')])
2025-08-02 11:10:39,321 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 11:10:39,321 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:39,322 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:39,322 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:39,322 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:39,322 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:39,323 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:39,323 - amazon_affiliate_integration.clients.openai_client - DEBUG - OpenAI returned products for '17. Personalize with Custom Name Signs or Monogram Decor': ['Custom Wooden Name Sign', 'Personalized Metal Monogram Wall Art']
2025-08-02 11:10:39,323 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '17. Personalize with Custom Name Signs or Monogram Decor': ['Custom Wooden Name Sign', 'Personalized Metal Monogram Wall Art']
2025-08-02 11:10:39,428 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '1. Create a Gallery Wall with Trendy Prints and Quotes': ['Framed Wall Art Prints Set', 'Custom Motivational Quote Wall Canvas']
2025-08-02 11:10:39,429 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '2. Incorporate Colorful Peel-and-Stick Wall Decals': ['WallDecal Warehouse Large Colorful Peel and Stick Wall Decals', 'RoomMates Neon Colorful Wall Decals Set']
2025-08-02 11:10:39,430 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '3. Use Floating Shelves for Curated Decor and Accessories': ['Floating Wall Shelves Set of 2 or More', 'Minimalist Black or White Wall Mount Book Shelves']
2025-08-02 11:10:39,430 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '4. Hang String Lights for Cozy Ambiance': ['Globe String Lights for Indoor Decor', 'Vintage Edison Bulb String Lights']
2025-08-02 11:10:39,431 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '5. Personalize with DIY Canvas Art or Painted Signs': ['Framed Abstract Canvas Wall Art', 'Custom Motivational Quote Canvas Print']
2025-08-02 11:10:39,431 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '6. Incorporate Multi-Functional Furniture with Hidden Storage': ['Storage Bed with Drawers', 'Multi-Functional Study Desk with Hidden Compartments']
2025-08-02 11:10:39,432 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '7. Use Color-Themed Bedding and Pillow Arrangements': ['Luxury Egyptian Cotton Bed Sheets', 'High-Quality Designer Decorative Throw Pillows']
2025-08-02 11:10:39,432 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '8. Install a Chic Vanity or Mirror Corner for Glam Moments': ['Hollywood Vanity Mirror with Lights', 'Adjustable LED Ring Light for Makeup and Selfies']
2025-08-02 11:10:39,433 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '9. Display Personal Collections in Open-Style Shelving': ['Floating Wall Shelves Set of 3', 'Tempered Glass Display Cases for Collectibles']
2025-08-02 11:10:39,433 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '10. Incorporate Metallic or Glitter Accents for a Touch of Glam': ['Metallic Table Lamp with Gold Finish', 'Glitter Photo Frame with Silver Accents']
2025-08-02 11:10:39,434 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '11. Create a Cozy Reading Nook with Comfy Seating': ['Lazy Boy Reese Leather Recliner', 'Safavieh Adirondack Collection Vintage Blue Wool Rug']
2025-08-02 11:10:39,434 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '12. Hang Inspirational Motto Banners or Wall Art': ['Inspirational Wall Art Canvas Prints', 'Custom Fabric Banner with Uplifting Quote']
2025-08-02 11:10:39,435 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '13. Add a DIY Headboard Using Upholstered Fabric or Fairy Lights': ['Upholstered Fabric Headboard', 'LED Fairy Lights String Set']
2025-08-02 11:10:39,435 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '14. Use Colorful Storage Bins and Organizers for Practicality': ['Stackable Plastic Storage Bins with Lids', 'Multi-Compartment Desk Organizer Bliers']
2025-08-02 11:10:39,436 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '15. Incorporate a Chalkboard or Whiteboard Wall for Creativity': ['Whiteboard Wall Paint Kit', 'Commercial Chalkboard Wall Panel System']
2025-08-02 11:10:39,436 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '16. Decorate with Trendy Tapestry or Textile Wall Hangings': ['Large Woven Tapestry Wall Hanging', 'Bohemian Textile Wall Art Decor']
2025-08-02 11:10:39,437 - amazon_affiliate_integration.processors.content_processor - DEBUG - Inserted product section with 2 products after heading '17. Personalize with Custom Name Signs or Monogram Decor': ['Custom Wooden Name Sign', 'Personalized Metal Monogram Wall Art']
2025-08-02 11:10:39,437 - amazon_affiliate_integration.processors.content_processor - INFO - Inserted 34 product sections into content
2025-08-02 11:10:39,437 - amazon_affiliate_integration.processors.content_processor - INFO - Successfully processed article 303 in 26.52s. Added 34 shortcodes for 17 sections.
2025-08-02 11:10:39,438 - amazon_affiliate_integration.processors.content_processor - INFO - Batch processing completed: 4 successful, 0 failed, 1 skipped
2025-08-02 11:10:39,438 - amazon_affiliate_integration - INFO - Updating WordPress content for cozytones.com...
2025-08-02 11:10:39,438 - amazon_affiliate_integration.clients.wordpress_client - INFO - Starting batch update of 4 articles on cozytones.com
2025-08-02 11:10:40,338 - httpcore.connection - DEBUG - connect_tcp.started host='cozytones.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:40,338 - httpcore.connection - DEBUG - connect_tcp.started host='cozytones.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:40,338 - httpcore.connection - DEBUG - connect_tcp.started host='cozytones.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:40,347 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223164E0>
2025-08-02 11:10:40,347 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232B4D0> server_hostname='cozytones.com' timeout=30.0
2025-08-02 11:10:40,347 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C2233D700>
2025-08-02 11:10:40,348 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C222B0050> server_hostname='cozytones.com' timeout=30.0
2025-08-02 11:10:40,348 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A6360>
2025-08-02 11:10:40,348 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232A5D0> server_hostname='cozytones.com' timeout=30.0
2025-08-02 11:10:40,361 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C222C6270>
2025-08-02 11:10:40,363 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:40,364 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A7320>
2025-08-02 11:10:40,364 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:40,364 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:40,365 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:40,367 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:40,367 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:40,367 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:40,368 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:40,368 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A7AD0>
2025-08-02 11:10:40,368 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:40,369 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:40,369 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:40,369 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:40,369 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:40,370 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:40,370 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:40,941 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:41 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'Link', b'<https://cozytones.com/wp-json/>; rel="https://api.w.org/"'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'Allow', b'GET, POST, PUT, PATCH, DELETE'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=Zz0GpozkXaOMPDlCILei6QBgHmXLk%2B1x8wMDUSxVaOPkZeLUsnLf%2B1T5DLbFJEW%2B3Jt650KgHIjgimEjnzXI%2FI282XZyumoSUXPpQHo%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'968b11f6c9d4ba5f-DAC')])
2025-08-02 11:10:40,941 - httpx - INFO - HTTP Request: POST https://cozytones.com/wp-json/wp/v2/posts/300 "HTTP/1.1 200 OK"
2025-08-02 11:10:40,942 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:40,945 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:40,945 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:40,946 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:40,946 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:40,946 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:40,947 - amazon_affiliate_integration.clients.wordpress_client - INFO - Successfully updated article 300 on cozytones.com
2025-08-02 11:10:41,171 - httpcore.connection - DEBUG - connect_tcp.started host='cozytones.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:10:41,172 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:41 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'Link', b'<https://cozytones.com/wp-json/>; rel="https://api.w.org/"'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'Allow', b'GET, POST, PUT, PATCH, DELETE'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=yY8NwtXa77CdpkPGzkGC3RwRbvkWOz42L5NsJrIN9ca8NPDc960ITTPdOxuBw2ryFlAU8neGPNy7re5VyzYoksdxuiwFk04aVvs8Exw%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'968b11f6cf0333bc-DAC')])
2025-08-02 11:10:41,173 - httpx - INFO - HTTP Request: POST https://cozytones.com/wp-json/wp/v2/posts/301 "HTTP/1.1 200 OK"
2025-08-02 11:10:41,173 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:41,175 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:41,176 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:41,176 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:41,176 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:41,177 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:41,178 - amazon_affiliate_integration.clients.wordpress_client - INFO - Successfully updated article 301 on cozytones.com
2025-08-02 11:10:41,184 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A6150>
2025-08-02 11:10:41,184 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000020C2232B050> server_hostname='cozytones.com' timeout=30.0
2025-08-02 11:10:41,198 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000020C223A68D0>
2025-08-02 11:10:41,199 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:10:41,199 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:10:41,200 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:10:41,200 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:10:41,200 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:10:41,399 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:41 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'Link', b'<https://cozytones.com/wp-json/>; rel="https://api.w.org/"'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'Allow', b'GET, POST, PUT, PATCH, DELETE'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=zCRDv%2FJAcHH0Eh2e77Kh%2Bdvuu7ytA1fKU1cKjIl2BGO1yH2y594MuK2n0PvH3dzFR9TtL8Z8SqfLvLmqdCX7xNVriTCuwQcm6Uwzh%2Fk%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'968b11f6cface87e-DAC')])
2025-08-02 11:10:41,400 - httpx - INFO - HTTP Request: POST https://cozytones.com/wp-json/wp/v2/posts/302 "HTTP/1.1 200 OK"
2025-08-02 11:10:41,400 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:41,405 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:41,406 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:41,406 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:41,406 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:41,407 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:41,407 - amazon_affiliate_integration.clients.wordpress_client - INFO - Successfully updated article 302 on cozytones.com
2025-08-02 11:10:41,914 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:10:41 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'Link', b'<https://cozytones.com/wp-json/>; rel="https://api.w.org/"'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'Allow', b'GET, POST, PUT, PATCH, DELETE'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=XVbdbC%2BgemwfJvozmeQqPbJbUcqBd3h1h%2B3Ifsbqx6GH28N31akB0vbeJRenztm7ppxS7a%2FQOeCV7Gijjt%2BR2wimyMa1ejvd1Pnu%2BYg%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'968b11fc0d67f257-DAC')])
2025-08-02 11:10:41,915 - httpx - INFO - HTTP Request: POST https://cozytones.com/wp-json/wp/v2/posts/303 "HTTP/1.1 200 OK"
2025-08-02 11:10:41,915 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:10:41,921 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:10:41,922 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:10:41,922 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:10:41,922 - httpcore.connection - DEBUG - close.started
2025-08-02 11:10:41,923 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:10:41,923 - amazon_affiliate_integration.clients.wordpress_client - INFO - Successfully updated article 303 on cozytones.com
2025-08-02 11:10:41,923 - amazon_affiliate_integration.clients.wordpress_client - INFO - Batch update completed on cozytones.com: 4 successful, 0 failed
2025-08-02 11:10:41,923 - amazon_affiliate_integration - INFO - WordPress updates completed: 4 successful, 0 failed
2025-08-02 11:10:41,924 - amazon_affiliate_integration.processors.state_manager - DEBUG - Created backup: D:\Amazon_Addon_For_Pinterest_Automation_v2\processed_urls.bak.20250802_111041
2025-08-02 11:10:41,927 - amazon_affiliate_integration.processors.state_manager - DEBUG - Successfully wrote D:\Amazon_Addon_For_Pinterest_Automation_v2\processed_urls.json
2025-08-02 11:10:41,927 - amazon_affiliate_integration - INFO - Updated state with 4 newly processed URLs
2025-08-02 11:10:42,204 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Post-processing checkpoint for cozytones.com (4 articles processed)
2025-08-02 11:10:42,204 - amazon_affiliate_integration - INFO - Domain processing completed for cozytones.com: 4 processed, 0 failed, 1 skipped in 34.37s
