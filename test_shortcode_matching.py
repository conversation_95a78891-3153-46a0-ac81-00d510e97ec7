#!/usr/bin/env python3
"""
Test shortcode matching between browser detection and cleanup patterns
"""

import re

# Browser crawler pattern (detects visible shortcodes with Unicode quotes)
browser_pattern = re.compile(
    r'\[amazon bestseller=[\u201d\u2033]([^\u201d\u2033]+)[\u201d\u2033][^\]]*\]',
    re.IGNORECASE | re.MULTILINE
)

# Cleanup processor patterns (expects Gutenberg blocks with ASCII quotes)
cleanup_block_pattern = re.compile(
    r'<!-- wp:heading -->\s*'
    r'<h3[^>]*class="wp-block-heading"[^>]*>[^<]*</h3>\s*'
    r'<!-- /wp:heading -->\s*'
    r'<!-- wp:shortcode -->\s*'
    r'\[amazon bestseller="[^"]+"[^\]]*\]\s*'
    r'<!-- /wp:shortcode -->',
    re.IGNORECASE | re.MULTILINE | re.DOTALL
)

cleanup_shortcode_pattern = re.compile(
    r'<!-- wp:shortcode -->\s*'
    r'\[amazon bestseller="([^"]+)"[^\]]*\]\s*'
    r'<!-- /wp:shortcode -->',
    re.IGNORECASE | re.MULTILINE | re.DOTALL
)

# Test data
# What browser sees (rendered HTML with visible shortcodes - Unicode quotes)
browser_content = '''
<h3 class="wp-block-heading">1. Reclaimed Wood Headboard with Weathered Finish</h3>
[amazon bestseller="Reclaimed Wood Headboard" filterby="price" filter="1000" filter_compare="less" items="1" template="table" tracking_id="piatto-20"]

<h3 class="wp-block-heading">3. Linen Bedding in Soft Neutral Tones</h3>
[amazon bestseller="Linen Duvet Cover Set" filterby="price" filter="1000" filter_compare="less" items="1" template="table" tracking_id="piatto-20"]
'''

# What WordPress editor contains (Gutenberg blocks)
wordpress_content = '''
<!-- wp:heading -->
<h3 class="wp-block-heading">1. Reclaimed Wood Headboard with Weathered Finish</h3>
<!-- /wp:heading -->

<!-- wp:shortcode -->
[amazon bestseller="Reclaimed Wood Headboard" filterby="price" filter="1000" filter_compare="less" items="1" template="table" tracking_id="piatto-20"]
<!-- /wp:shortcode -->

<!-- wp:heading -->
<h3 class="wp-block-heading">3. Linen Bedding in Soft Neutral Tones</h3>
<!-- /wp:heading -->

<!-- wp:shortcode -->
[amazon bestseller="Linen Duvet Cover Set" filterby="price" filter="1000" filter_compare="less" items="1" template="table" tracking_id="piatto-20"]
<!-- /wp:shortcode -->
'''

print("=== Browser Detection Test ===")
browser_matches = browser_pattern.findall(browser_content)
print(f"Browser pattern found: {browser_matches}")

print("\n=== WordPress Cleanup Test ===")
cleanup_block_matches = cleanup_block_pattern.findall(wordpress_content)
print(f"Cleanup block pattern found: {len(cleanup_block_matches)} blocks")

cleanup_shortcode_matches = cleanup_shortcode_pattern.findall(wordpress_content)
print(f"Cleanup shortcode pattern found: {cleanup_shortcode_matches}")

print("\n=== Matching Test ===")
print("Products detected by browser:", browser_matches)
print("Products in WordPress content:", cleanup_shortcode_matches)

# Test if they match
for browser_product in browser_matches:
    if browser_product in cleanup_shortcode_matches:
        print(f"✓ Match found: {browser_product}")
    else:
        print(f"✗ No match for: {browser_product}")
