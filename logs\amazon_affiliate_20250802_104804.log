2025-08-02 10:48:04,064 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_104804.log
2025-08-02 10:48:04,065 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_104804.log
2025-08-02 10:48:04,065 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-08-02 10:48:04,065 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-08-02 10:48:04,066 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-08-02 10:48:04,075 - __main__ - INFO - Starting complete workflow: Process → Crawl → Cleanup...
2025-08-02 10:48:04,075 - amazon_affiliate_integration - INFO - 🚀 Starting COMPLETE workflow: Process → Crawl → Cleanup
2025-08-02 10:48:04,075 - amazon_affiliate_integration - INFO - 📝 Phase 1: Processing articles to add Amazon shortcodes...
2025-08-02 10:48:04,076 - amazon_affiliate_integration - INFO - Starting domain processing: cozytones.com (force=False, dry_run=False, limit=1)
2025-08-02 10:48:04,076 - amazon_affiliate_integration - INFO - Loaded 0 previously processed URLs
2025-08-02 10:48:04,076 - amazon_affiliate_integration - INFO - Loaded 0 excluded URLs
2025-08-02 10:48:04,076 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress client initialized for cozytones.com (https://cozytones.com)
2025-08-02 10:48:05,549 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?per_page=1 "HTTP/1.1 200 OK"
2025-08-02 10:48:05,552 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress API connection test successful for cozytones.com
2025-08-02 10:48:05,552 - amazon_affiliate_integration - INFO - Fetching articles from cozytones.com...
2025-08-02 10:48:05,552 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetching articles from cozytones.com since 2025-01-01T00:00:00
2025-08-02 10:48:08,117 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?page=1&per_page=100&after=2025-01-01T00%3A00%3A00&status=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-08-02 10:48:09,666 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetched 100 articles from page 1
2025-08-02 10:48:09,666 - amazon_affiliate_integration.clients.wordpress_client - INFO - Limited to 1 articles for testing
2025-08-02 10:48:09,666 - amazon_affiliate_integration.clients.wordpress_client - INFO - Total articles fetched from cozytones.com: 1
2025-08-02 10:48:09,668 - amazon_affiliate_integration - INFO - Converted 1 articles to ArticleInfo objects
2025-08-02 10:48:09,668 - amazon_affiliate_integration - INFO - Normal mode: processing 1 articles (skipping 0 processed, 0 excluded)
2025-08-02 10:48:10,017 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Pre-processing checkpoint for cozytones.com (1 articles)
2025-08-02 10:48:10,017 - amazon_affiliate_integration - INFO - Created git checkpoint before processing cozytones.com
2025-08-02 10:48:10,017 - amazon_affiliate_integration - INFO - Processing 1 articles for cozytones.com...
2025-08-02 10:48:10,018 - amazon_affiliate_integration.processors.content_processor - INFO - Starting batch processing of 1 articles with concurrency limit 4
2025-08-02 10:48:10,018 - amazon_affiliate_integration.processors.content_processor - INFO - Processing article 304: 12 Modern Farmhouse Bedroom Ideas for Rustic Elegance
2025-08-02 10:48:10,021 - amazon_affiliate_integration.utils.backup_manager - INFO - Created backup: cozytones.com_304_pre_processing_20250802_104810.json
2025-08-02 10:48:10,023 - amazon_affiliate_integration.processors.content_processor - INFO - Generating products for 12 sections using batch processing
2025-08-02 10:48:10,023 - amazon_affiliate_integration.processors.content_processor - INFO - Using individual processing for reliable product generation
2025-08-02 10:48:11,142 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:48:11,146 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '1. Reclaimed Wood Headboard with Weathered Finish': ['Reclaimed Wood Headboard', 'Rustic Wooden Bed Frame']
2025-08-02 10:48:13,175 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:48:13,182 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '2. Industrial-Style Lighting with Matte Black Fixtures': ['Westinghouse Iron Hill 1-Light Pendant Light', 'Kichler 42325OZ Brinley Wall Sconce']
2025-08-02 10:48:14,365 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:48:14,371 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '3. Linen Bedding in Soft Neutral Tones': ['Linen Duvet Cover Set', 'Luxury Wool Throw Blanket']
2025-08-02 10:48:16,080 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:48:16,084 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '4. Vintage-Inspired Nightstands with Distressed Paint': ['Vintage Distressed Nightstand', 'Pastel Painted Wooden Nightstand']
2025-08-02 10:48:17,025 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:48:17,027 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '5. Neutral Area Rug with Subtle Pattern': ['NuLOOM Moroccan Blythe Area Rug', 'Safavieh Hudson Shag Collection Plush Area Rug']
2025-08-02 10:48:18,583 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:48:18,592 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '6. Classic Barn Door Sliding Closet Entrance': ['Reclaimed Wood Barn Door', 'Black Iron Sliding Door Hardware Kit']
2025-08-02 10:48:19,692 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:48:19,697 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '7. Minimalist Modern Bed Frame in Matte Finish': ['Modern Matte Black Metal Bed Frame', 'Minimalist Low Profile Platform Bed Frame']
2025-08-02 10:48:21,311 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:48:21,312 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '8. Mason Jar or Rustic Lanterns for Ambient Lighting': ['Vintage Mason Jar Pendant Light Fixtures', 'Rustic Metal Lanterns with Candle-Style LED Bulbs']
2025-08-02 10:48:22,755 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:48:22,767 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '9. Simple Linen-Covered Window Treatments': ['Linen Window Curtains', 'Rustic Iron Curtain Rods']
2025-08-02 10:48:24,324 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:48:24,325 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '10. Layered Bedding with Cozy Throws and Pillows': ['Linen Throw Pillow Set', 'Chunky Knit Throw Blanket']
2025-08-02 10:48:25,911 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:48:25,917 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '11. Framed Vintage Botanical Prints or Sketches': ['Vintage Botanical Framed Art Prints', 'Distressed Wooden Picture Frames']
2025-08-02 10:48:27,377 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 10:48:27,378 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '12. Practical yet Stylish Storage Solutions with Open Wardrobes': ['Wooden Clothing Rack with Distressed Finish', 'Open Wooden Shelving Units for Storage']
2025-08-02 10:48:27,490 - amazon_affiliate_integration.processors.content_processor - INFO - Inserted 12 product sections into content
2025-08-02 10:48:27,490 - amazon_affiliate_integration.processors.content_processor - INFO - Successfully processed article 304 in 17.47s. Added 12 shortcodes for 12 sections.
2025-08-02 10:48:27,491 - amazon_affiliate_integration.processors.content_processor - INFO - Batch processing completed: 1 successful, 0 failed, 0 skipped
2025-08-02 10:48:27,491 - amazon_affiliate_integration - INFO - Updating WordPress content for cozytones.com...
2025-08-02 10:48:27,491 - amazon_affiliate_integration.clients.wordpress_client - INFO - Starting batch update of 1 articles on cozytones.com
2025-08-02 10:48:29,064 - httpx - INFO - HTTP Request: POST https://cozytones.com/wp-json/wp/v2/posts/304 "HTTP/1.1 200 OK"
2025-08-02 10:48:29,070 - amazon_affiliate_integration.clients.wordpress_client - INFO - Successfully updated article 304 on cozytones.com
2025-08-02 10:48:29,070 - amazon_affiliate_integration.clients.wordpress_client - INFO - Batch update completed on cozytones.com: 1 successful, 0 failed
2025-08-02 10:48:29,070 - amazon_affiliate_integration - INFO - WordPress updates completed: 1 successful, 0 failed
2025-08-02 10:48:29,073 - amazon_affiliate_integration - INFO - Updated state with 1 newly processed URLs
2025-08-02 10:48:29,302 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Post-processing checkpoint for cozytones.com (1 articles processed)
2025-08-02 10:48:29,302 - amazon_affiliate_integration - INFO - Domain processing completed for cozytones.com: 1 processed, 0 failed, 0 skipped in 25.23s
2025-08-02 10:48:29,303 - amazon_affiliate_integration - INFO - ✅ Phase 1 completed successfully
2025-08-02 10:48:29,303 - amazon_affiliate_integration - INFO - 🕷️ Phase 2 & 3: Crawling and cleaning up failed shortcodes...
2025-08-02 10:48:29,303 - amazon_affiliate_integration - INFO - Starting crawl and cleanup workflow...
2025-08-02 10:48:29,303 - amazon_affiliate_integration - INFO - Phase 1: Crawling articles for visible shortcodes...
2025-08-02 10:48:29,304 - amazon_affiliate_integration - INFO - Using browser crawler for cozytones.com
2025-08-02 10:48:29,835 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser initialized with 4 concurrent pages
2025-08-02 10:48:29,835 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Starting browser crawl of 1 articles...
2025-08-02 10:48:35,174 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Progress: 1/1 articles crawled
2025-08-02 10:48:35,174 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser crawl completed: 1/1 successful in 5.34s (0.2 articles/sec)
2025-08-02 10:48:35,252 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser resources cleaned up
2025-08-02 10:48:35,252 - amazon_affiliate_integration - INFO - Crawled 1 articles for domain cozytones.com, found 12 visible shortcodes
2025-08-02 10:48:35,257 - amazon_affiliate_integration - INFO - Phase 2: Cleaning up failed shortcodes...
2025-08-02 10:48:35,257 - amazon_affiliate_integration - INFO - Crawl and cleanup workflow completed successfully in 5.95s
2025-08-02 10:48:35,257 - amazon_affiliate_integration - INFO - ✅ Phase 2 & 3 completed successfully
2025-08-02 10:48:35,257 - amazon_affiliate_integration - INFO - 🎉 COMPLETE workflow finished successfully in 31.18s
2025-08-02 10:48:35,257 - __main__ - INFO - Complete workflow finished successfully
