"""
Browser-based Shortcode Crawler for Amazon Affiliate Integration

Uses <PERSON>wright to launch a real browser and detect visible shortcodes as they appear
to actual users, including JavaScript-rendered content.
"""

import asyncio
import logging
import subprocess
import sys
import time
from datetime import datetime
from typing import List, Dict, Optional, Set
from urllib.parse import urljoin, urlparse

try:
    from playwright.async_api import async_playwright, <PERSON><PERSON><PERSON>, BrowserContext, Page
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False

from ..core.models import (
    CrawlerResult, VisibleShortcode, CrawlerStatus, ArticleInfo
)
from ..core.config import CRAWLER_CONFIG


class BrowserShortcodeCrawler:
    """
    Browser-based crawler using <PERSON>wright to detect visible shortcodes
    
    Features:
    - Real browser rendering with JavaScript execution
    - Automatic Playwright installation if missing
    - Headless Chromium for performance
    - Configurable concurrency and timeouts
    - Comprehensive error handling
    """
    
    def __init__(self, concurrency_limit: int = None, headless: bool = True):
        """
        Initialize browser crawler

        Args:
            concurrency_limit: Maximum concurrent browser pages
            headless: Whether to run browser in headless mode (default: True)
        """
        self.concurrency_limit = concurrency_limit or CRAWLER_CONFIG['concurrency_limit']
        self.timeout = CRAWLER_CONFIG['timeout'] * 1000  # Convert to milliseconds for Playwright
        self.request_delay = CRAWLER_CONFIG['request_delay']
        self.max_retries = CRAWLER_CONFIG['max_retries']
        self.headless = headless

        self.logger = logging.getLogger(__name__)
        self.browser = None
        self.context = None
        self.semaphore = None

        # Ensure Playwright is available
        self._ensure_playwright_available()
    
    def _ensure_playwright_available(self):
        """Ensure Playwright is installed and browsers are available"""
        global PLAYWRIGHT_AVAILABLE
        if not PLAYWRIGHT_AVAILABLE:
            self.logger.error("Playwright not installed. Installing...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "playwright"])
                self.logger.info("✓ Playwright installed successfully")

                # Install browser binaries
                subprocess.check_call([sys.executable, "-m", "playwright", "install", "chromium"])
                self.logger.info("✓ Chromium browser installed successfully")

                # Try importing again
                from playwright.async_api import async_playwright
                PLAYWRIGHT_AVAILABLE = True

            except subprocess.CalledProcessError as e:
                self.logger.error(f"Failed to install Playwright: {e}")
                raise RuntimeError("Could not install Playwright automatically")
    
    async def __aenter__(self):
        """Async context manager entry"""
        await self._initialize_browser()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        await self._cleanup_browser()
    
    async def _initialize_browser(self):
        """Initialize Playwright browser and context"""
        try:
            self.playwright = await async_playwright().start()
            
            # Launch Chromium browser
            self.browser = await self.playwright.chromium.launch(
                headless=self.headless,  # Configurable headless mode
                args=[
                    '--no-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-gpu',
                    '--disable-web-security',
                    '--disable-features=VizDisplayCompositor'
                ]
            )
            
            # Create browser context with realistic settings
            self.context = await self.browser.new_context(
                viewport={'width': 1920, 'height': 1080},
                user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
            )
            
            # Set default timeout
            self.context.set_default_timeout(self.timeout)
            
            self.semaphore = asyncio.Semaphore(self.concurrency_limit)
            self.logger.info(f"✓ Browser initialized with {self.concurrency_limit} concurrent pages")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize browser: {e}")
            raise
    
    async def _cleanup_browser(self):
        """Clean up browser resources"""
        try:
            if self.context:
                await self.context.close()
            if self.browser:
                await self.browser.close()
            if hasattr(self, 'playwright'):
                await self.playwright.stop()
            self.logger.info("✓ Browser resources cleaned up")
        except Exception as e:
            self.logger.warning(f"Error during browser cleanup: {e}")
    
    async def crawl_articles(self, articles: List[ArticleInfo]) -> List[CrawlerResult]:
        """
        Crawl multiple articles using browser automation
        
        Args:
            articles: List of articles to crawl
            
        Returns:
            List of crawler results
        """
        if not articles:
            self.logger.info("No articles to crawl")
            return []
        
        self.logger.info(f"Starting browser crawl of {len(articles)} articles...")
        start_time = time.time()
        
        # Create crawling tasks
        tasks = [self._crawl_single_article(article) for article in articles]
        
        # Execute with progress reporting
        results = []
        completed = 0
        
        for coro in asyncio.as_completed(tasks):
            try:
                result = await coro
                results.append(result)
                completed += 1
                
                if completed % 5 == 0 or completed == len(articles):
                    self.logger.info(f"Progress: {completed}/{len(articles)} articles crawled")
                    
            except Exception as e:
                self.logger.error(f"Error in crawling task: {e}")
                # Create failed result
                results.append(CrawlerResult(
                    url="unknown",
                    domain="unknown", 
                    status=CrawlerStatus.FAILED,
                    error_message=str(e)
                ))
        
        total_time = time.time() - start_time
        successful = sum(1 for r in results if r.success)
        
        self.logger.info(
            f"✓ Browser crawl completed: {successful}/{len(results)} successful "
            f"in {total_time:.2f}s ({len(results)/total_time:.1f} articles/sec)"
        )
        
        return results

    async def _crawl_single_article(self, article: ArticleInfo) -> CrawlerResult:
        """
        Crawl a single article using browser automation

        Args:
            article: Article information

        Returns:
            Crawler result
        """
        start_time = time.time()

        async with self.semaphore:
            try:
                # Add delay between requests
                if self.request_delay > 0:
                    await asyncio.sleep(self.request_delay)

                # Create new page for this article
                page = await self.context.new_page()

                try:
                    # Navigate to article URL
                    await page.goto(article.url, wait_until='domcontentloaded')

                    # Wait a bit for any dynamic content to load
                    await asyncio.sleep(1)

                    # Get page content after JavaScript execution
                    content = await page.content()

                    # Detect visible shortcodes in the rendered content
                    visible_shortcodes = self._detect_visible_shortcodes(content)

                    return CrawlerResult(
                        url=article.url,
                        domain=article.domain,
                        status=CrawlerStatus.CRAWLED,
                        visible_shortcodes=visible_shortcodes,
                        total_shortcodes_found=len(visible_shortcodes),
                        response_time=time.time() - start_time
                    )

                finally:
                    # Always close the page to free resources
                    await page.close()

            except Exception as e:
                self.logger.warning(f"Error crawling {article.url}: {e}")
                return CrawlerResult(
                    url=article.url,
                    domain=article.domain,
                    status=CrawlerStatus.FAILED,
                    error_message=str(e),
                    response_time=time.time() - start_time
                )

    def _detect_visible_shortcodes(self, content: str) -> List[VisibleShortcode]:
        """
        Detect visible Amazon shortcodes in rendered HTML content

        Args:
            content: Rendered HTML content from browser

        Returns:
            List of visible shortcodes found
        """
        import re

        visible_shortcodes = []

        # Pattern to match Amazon shortcodes - handle all quote variations
        # Matches: right double quotation mark (") and double prime (″) - U+201D and U+2033
        shortcode_pattern = re.compile(
            r'\[amazon bestseller=[\u201d\u2033]([^\u201d\u2033]+)[\u201d\u2033][^\]]*\]',
            re.IGNORECASE | re.MULTILINE
        )

        # Pattern to extract heading context
        heading_pattern = re.compile(
            r'<h3[^>]*class="wp-block-heading"[^>]*>([^<]+)</h3>',
            re.IGNORECASE | re.MULTILINE
        )

        # Debug: Log content snippet to understand what we're searching
        if self.logger.isEnabledFor(logging.DEBUG):
            content_snippet = content[:2000] if len(content) > 2000 else content
            self.logger.debug(f"Content snippet for shortcode detection: {content_snippet}")

        # Debug: Check if content contains shortcodes at all
        shortcode_count = content.count('[amazon bestseller=')
        self.logger.debug(f"Content contains {shortcode_count} '[amazon bestseller=' strings")

        # Search for shortcodes in HTML content
        shortcode_matches_html = list(shortcode_pattern.finditer(content))
        self.logger.debug(f"Regex pattern found {len(shortcode_matches_html)} matches in HTML")

        # Also extract text content manually to catch visible shortcodes (when AAWP plugin is disabled)
        # Simple HTML tag removal for text extraction
        import re as re_module
        text_content = re_module.sub(r'<[^>]+>', '', content)
        text_content = re_module.sub(r'\s+', ' ', text_content)  # Normalize whitespace

        shortcode_matches_text = list(shortcode_pattern.finditer(text_content))

        # Combine matches, removing duplicates based on product name
        seen_products = set()
        all_matches = []

        for match in shortcode_matches_html + shortcode_matches_text:
            product_name = match.group(1)
            if product_name not in seen_products:
                seen_products.add(product_name)
                all_matches.append(match)

        self.logger.debug(f"Found {len(shortcode_matches_html)} shortcodes in HTML, {len(shortcode_matches_text)} in text, {len(all_matches)} unique")

        if not all_matches:
            self.logger.debug("No visible shortcodes found in content")
            return visible_shortcodes

        # Find all heading matches for context
        heading_matches = list(heading_pattern.finditer(content))

        for match in all_matches:
            product_name = match.group(1)
            full_shortcode = match.group(0)
            position = match.start()

            # Find the closest preceding heading
            heading = self._find_closest_heading(position, heading_matches)

            visible_shortcode = VisibleShortcode(
                product_name=product_name,
                heading=heading,
                full_shortcode=full_shortcode,
                position_in_content=position
            )

            visible_shortcodes.append(visible_shortcode)

            self.logger.debug(
                f"Found visible shortcode: '{product_name}' "
                f"under heading: '{heading}' at position {position}"
            )

        return visible_shortcodes

    def _find_closest_heading(self, position: int, heading_matches: List) -> str:
        """
        Find the closest heading before the given position

        Args:
            position: Position in content to search from
            heading_matches: List of heading regex matches

        Returns:
            Closest heading text or empty string
        """
        closest_heading = ""
        closest_distance = float('inf')

        for heading_match in heading_matches:
            heading_position = heading_match.start()

            # Only consider headings that come before the shortcode
            if heading_position < position:
                distance = position - heading_position
                if distance < closest_distance:
                    closest_distance = distance
                    closest_heading = heading_match.group(1).strip()

        return closest_heading
