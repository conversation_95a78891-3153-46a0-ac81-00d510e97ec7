#!/usr/bin/env python3
"""
Test script for failed product section cleanup functionality

This script demonstrates the new cleanup methods for handling AAWP plugin failures:
- Scenario 1: AAWP deactivated/crashed (raw shortcode visible)
- Scenario 2b: AAWP active but no products found (orphaned H3 headings)
"""

import re
import logging
from amazon_affiliate_integration.processors.cleanup_processor import CleanupProcessor

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_scenario_1_note():
    """Note about Scenario 1 detection"""
    print("\n=== Note about Scenario 1: Deactivated AAWP Detection ===")
    print("Scenario 1 (AAWP deactivated/crashed) cannot be detected from WordPress content alone.")
    print("It requires crawler results that show visible shortcodes in rendered HTML.")
    print("The existing cleanup_failed_shortcodes() method handles this by removing")
    print("the entire PRODUCT_SECTION_TEMPLATE when visible shortcodes are detected.")
    print("✅ This is handled by the existing visible shortcode cleanup logic.")

def test_scenario_2b_detection():
    """Test detection of failed product sections (Scenario 2b)"""
    print("\n=== Testing Scenario 2b: Failed Product Section Detection ===")
    
    # Sample content with failed product section (H3 followed by paragraph)
    content_with_failed_section = '''
<!-- wp:paragraph -->
<p>Here's some regular content before the failed section.</p>
<!-- /wp:paragraph -->

<!-- wp:heading {"level":3} -->
<h3 class="wp-block-heading">Recommended Products to replicate this idea</h3>
<!-- /wp:heading -->

<!-- wp:paragraph -->
<p>This paragraph immediately follows the H3, indicating the shortcode disappeared.</p>
<!-- /wp:paragraph -->
'''
    
    # Test the pattern
    pattern = CleanupProcessor.FAILED_PRODUCT_SECTION_PATTERN
    matches = pattern.findall(content_with_failed_section)
    
    print(f"Found {len(matches)} failed product sections")
    
    # Test cleanup
    processor = CleanupProcessor(None, None)  # Mock objects for testing
    cleaned_content, sections_removed = processor.cleanup_failed_product_sections(content_with_failed_section)
    
    print(f"Sections removed: {sections_removed}")
    print(f"Content length before: {len(content_with_failed_section)}")
    print(f"Content length after: {len(cleaned_content)}")
    
    # Verify the section was removed
    if "Recommended Products to replicate this idea" not in cleaned_content:
        print("✅ SUCCESS: Failed product section was properly removed")
    else:
        print("❌ FAILED: Failed product section was not removed")

def test_working_section_preservation():
    """Test that working product sections are preserved"""
    print("\n=== Testing Working Section Preservation ===")

    # Sample content with working product section (H3 followed by shortcode, then paragraph)
    content_with_working_section = '''
<!-- wp:paragraph -->
<p>Here's some regular content before the working section.</p>
<!-- /wp:paragraph -->

<!-- wp:heading {"level":3} -->
<h3 class="wp-block-heading">Recommended Products to replicate this idea</h3>
<!-- /wp:heading -->

<!-- wp:shortcode -->
[amazon bestseller="modern bathroom vanity" filterby="price" filter="1000" filter_compare="less" items="1" template="table" tracking_id="piatto-20"]
<!-- /wp:shortcode -->

<!-- wp:paragraph -->
<p>This paragraph follows the shortcode, indicating it's working properly.</p>
<!-- /wp:paragraph -->
'''

    processor = CleanupProcessor(None, None)  # Mock objects for testing

    # Test only the failed product section cleanup (Scenario 2b)
    # Working sections should not be affected by this cleanup
    cleaned_content, sections_removed = processor.cleanup_failed_product_sections(content_with_working_section)

    print(f"Failed sections removed: {sections_removed}")
    print(f"Content length before: {len(content_with_working_section)}")
    print(f"Content length after: {len(cleaned_content)}")

    # Verify the working section was preserved
    if "Recommended Products to replicate this idea" in cleaned_content and sections_removed == 0:
        print("✅ SUCCESS: Working product section was properly preserved")
    else:
        print("❌ FAILED: Working product section was incorrectly removed")

def test_multiple_sections():
    """Test handling of multiple failed sections in one article"""
    print("\n=== Testing Multiple Failed Sections ===")

    # Sample content with multiple failed sections
    content_with_multiple_failures = '''
<!-- wp:paragraph -->
<p>First section of content.</p>
<!-- /wp:paragraph -->

<!-- wp:heading {"level":3} -->
<h3 class="wp-block-heading">Recommended Products to replicate this idea</h3>
<!-- /wp:heading -->

<!-- wp:paragraph -->
<p>This paragraph immediately follows the first H3, indicating the shortcode disappeared.</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph -->
<p>Middle section of content.</p>
<!-- /wp:paragraph -->

<!-- wp:heading {"level":3} -->
<h3 class="wp-block-heading">Recommended Products to replicate this idea</h3>
<!-- /wp:heading -->

<!-- wp:paragraph -->
<p>This paragraph immediately follows the second H3, indicating the shortcode disappeared.</p>
<!-- /wp:paragraph -->

<!-- wp:paragraph -->
<p>Final section of content.</p>
<!-- /wp:paragraph -->
'''

    processor = CleanupProcessor(None, None)  # Mock objects for testing

    # Test failed product section cleanup (Scenario 2b)
    original_content = content_with_multiple_failures
    cleaned_content, failed_removed = processor.cleanup_failed_product_sections(original_content)

    print(f"Failed product sections removed: {failed_removed}")
    print(f"Content length before: {len(original_content)}")
    print(f"Content length after: {len(cleaned_content)}")

    # Count remaining H3 headings
    remaining_h3_count = cleaned_content.count('Recommended Products to replicate this idea')
    print(f"Remaining H3 headings: {remaining_h3_count}")

    if remaining_h3_count == 0 and failed_removed == 2:
        print("✅ SUCCESS: All failed sections were properly removed")
    else:
        print(f"❌ FAILED: Expected 2 sections removed, got {failed_removed}. {remaining_h3_count} sections remain")

if __name__ == "__main__":
    print("Testing Failed Product Section Cleanup Functionality")
    print("=" * 60)
    
    try:
        test_scenario_1_note()
        test_scenario_2b_detection()
        test_working_section_preservation()
        test_multiple_sections()
        
        print("\n" + "=" * 60)
        print("All tests completed!")
        
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        raise
