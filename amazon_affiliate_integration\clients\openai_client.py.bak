"""
OpenAI Client for Amazon Affiliate Integration

Enhanced OpenAI client with improved error handling, rate limiting,
and product analysis capabilities.
"""

import asyncio
import logging
import httpx
from typing import List, Optional
import json

from ..core.config import (
    OPENAI_API_KEY, OPENAI_MODEL, OPENAI_MAX_TOKENS, 
    OPENAI_TEMPERATURE, API_RATE_LIMIT_DELAY
)
from ..utils.helpers import retry_async

class OpenAIClient:
    """
    Enhanced OpenAI API client for product analysis
    
    Features:
    - Automatic retry with exponential backoff
    - Rate limiting to prevent API abuse
    - Enhanced error handling and logging
    - Configurable model parameters
    """
    
    def __init__(self, api_key: str = None):
        """
        Initialize OpenAI client

        Args:
            api_key: OpenAI API key (default: from config)
        """
        self.api_key = api_key or OPENAI_API_KEY
        self.base_url = "https://api.openai.com/v1"
        self.model = OPENAI_MODEL
        self.logger = logging.getLogger(__name__)

        if not self.api_key:
            raise ValueError("OpenAI API key is required")

        # Rate limiting
        self.last_request_time = 0.0
        self.rate_limit_delay = API_RATE_LIMIT_DELAY

        self.logger.info(f"OpenAI client initialized with model: {self.model}")
    
    async def _rate_limit(self):
        """Apply rate limiting between requests"""
        current_time = asyncio.get_event_loop().time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.rate_limit_delay:
            sleep_time = self.rate_limit_delay - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = asyncio.get_event_loop().time()
    
    async def _make_request(self, messages: List[dict]) -> dict:
        """
        Make a request to OpenAI API with error handling
        
        Args:
            messages: List of message dictionaries
            
        Returns:
            API response dictionary
        """
        await self._rate_limit()
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": OPENAI_MODEL,
            "messages": messages,
            "max_tokens": OPENAI_MAX_TOKENS,
            "temperature": OPENAI_TEMPERATURE
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            response = await client.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            return response.json()
    
    async def analyze_products(self, heading: str, paragraph: str) -> List[str]:
        """
        Analyze heading and paragraph to identify 2 most expensive products
        
        Args:
            heading: H2 heading text
            paragraph: Second paragraph content
            
        Returns:
            List of 2 product names
        """
        prompt = f"""You are a product extraction specialist. Your task is to identify the 2 most expensive purchasable products for the specific application described in the heading and paragraph.

HEADING: {heading}
DESCRIPTION: {paragraph}

CRITICAL RULES:
1. Extract materials/items mentioned in the paragraph text
2. Convert them to PURCHASABLE PRODUCTS that fit the heading context
3. Do NOT extract raw materials (e.g., "silk", "velvet") - convert to finished products
4. Consider the heading context to determine the appropriate product category
5. Use searchable product names that Amazon would recognize
6. Rank by inherent expense within the context

CONTEXT CONVERSION EXAMPLES:
- If heading is about "Fabric-Covered Ceilings" and paragraph mentions "silk, velvet":
  Convert to: "Fabric Ceiling Panels", "Upholstered Ceiling Tiles"
- If heading is about "Throw Pillows" and paragraph mentions "velvet, cotton":
  Convert to: "Velvet Throw Pillows", "Cotton Throw Pillows"
- If heading is about "Rugs" and paragraph mentions "Persian, kilim":
  Convert to: "Persian Rug", "Kilim Rug"

Return ONLY the 2 product names in this exact format:
Product-1: [contextually appropriate finished product]
Product-2: [contextually appropriate finished product]

Focus on products that can actually be purchased on Amazon for the specific application mentioned in the heading."""
        
        messages = [
            {"role": "system", "content": "You are a product extraction specialist focused on identifying expensive, purchasable products."},
            {"role": "user", "content": prompt}
        ]
        
        async def make_request():
            return await self._make_request(messages)
        
        try:
            # Use retry mechanism for robustness
            result = await retry_async(
                make_request,
                max_retries=3,
                delay=1.0,
                backoff_factor=2.0,
                logger=self.logger
            )
            
            content = result['choices'][0]['message']['content'].strip()
            
            # Parse the response to extract product names
            products = []
            for line in content.split('\n'):
                if line.startswith('Product-'):
                    try:
                        product_name = line.split(':', 1)[1].strip()
                        if product_name:
                            products.append(product_name)
                    except IndexError:
                        continue
            
            if len(products) >= 2:
                self.logger.debug(f"OpenAI returned products for '{heading}': {products[:2]}")
                return products[:2]
            else:
                self.logger.warning(f"OpenAI returned fewer than 2 products: {content}")
                return products  # Return whatever products we got, even if fewer than 2

        except Exception as e:
            self.logger.error(f"OpenAI API error for heading '{heading}': {e}")
            return []  # Return empty list instead of fallback products
    
    async def analyze_products_batch(self, sections: List[dict]) -> dict:
        """
        Analyze multiple sections in a single batch API call for maximum efficiency

        Args:
            sections: List of section dictionaries with 'heading' and 'second_paragraph'

        Returns:
            Dictionary mapping headings to product lists
        """
        if not sections:
            return {}

        self.logger.info(f"Analyzing {len(sections)} sections in single batch API call")

        # Create batch prompt for all sections
        batch_prompt = """You are a product extraction specialist. For each section below, identify the 2 most expensive purchasable products for the specific application described.

CRITICAL RULES:
1. Extract materials/items mentioned in the paragraph text
2. Convert them to PURCHASABLE PRODUCTS that fit the heading context
3. Do NOT extract raw materials (e.g., "silk", "velvet") - convert to finished products
4. Consider the heading context to determine the appropriate product category
5. Use searchable product names that Amazon would recognize
6. Rank by inherent expense within the context

CONTEXT CONVERSION EXAMPLES:
- If heading is about "Fabric-Covered Ceilings" and paragraph mentions "silk, velvet":
  Convert to: "Fabric Ceiling Panels", "Upholstered Ceiling Tiles"
- If heading is about "Throw Pillows" and paragraph mentions "velvet, cotton":
  Convert to: "Velvet Throw Pillows", "Cotton Throw Pillows"
- If heading is about "Rugs" and paragraph mentions "Persian, kilim":
  Convert to: "Persian Rug", "Kilim Rug"

Return your response in this exact format for each section:
SECTION: [section_number]
Product-1: [contextually appropriate finished product]
Product-2: [contextually appropriate finished product]

Focus on products that can actually be purchased on Amazon for the specific application mentioned in the heading.

Sections to analyze:
"""

        # Add each section to the batch prompt
        for i, section in enumerate(sections, 1):
            batch_prompt += f"\nSECTION {i}:\nHeading: {section['heading']}\nContent: {section['second_paragraph']}\n"

        batch_prompt += "\nRemember: Return exactly 2 products per section in the specified format."

        # Make single API call for all sections
        async def make_request():
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }

            payload = {
                "model": self.model,
                "messages": [
                    {"role": "system", "content": "You are a product extraction specialist focused on identifying expensive, purchasable products."},
                    {"role": "user", "content": batch_prompt}
                ],
                "max_tokens": 4000,  # Increased for batch processing
                "temperature": 0.7
            }

            async with httpx.AsyncClient(timeout=60.0) as client:  # Increased timeout for batch
                response = await client.post(
                    f"{self.base_url}/chat/completions",
                    headers=headers,
                    json=payload
                )
                response.raise_for_status()
                return response.json()

        try:
            # Use retry mechanism for robustness
            result = await retry_async(
                make_request,
                max_retries=3,
                delay=1.0,
                backoff_factor=2.0,
                logger=self.logger
            )

            content = result['choices'][0]['message']['content'].strip()

            # Parse batch response
            results = {}
            current_section = None
            current_products = []

            for line in content.split('\n'):
                line = line.strip()
                if line.startswith('SECTION:'):
                    # Save previous section if exists
                    if current_section is not None and len(current_products) >= 2:
                        results[sections[current_section - 1]['heading']] = current_products[:2]

                    # Start new section
                    try:
                        current_section = int(line.split(':')[1].strip())
                        current_products = []
                    except (ValueError, IndexError):
                        continue

                elif line.startswith('Product-'):
                    try:
                        product_name = line.split(':', 1)[1].strip()
                        if product_name:
                            current_products.append(product_name)
                    except IndexError:
                        continue

            # Save last section
            if current_section is not None and len(current_products) >= 2:
                results[sections[current_section - 1]['heading']] = current_products[:2]

            # Handle missing sections without fallback products
            for section in sections:
                if section['heading'] not in results:
                    self.logger.warning(f"No products found for section '{section['heading']}'")
                    results[section['heading']] = []  # Return empty list instead of fallback products
                elif len(results[section['heading']]) < 2:
                    # Keep whatever products we got, even if fewer than 2
                    self.logger.warning(f"Only {len(results[section['heading']])} products found for section '{section['heading']}'")
                    # No need to fill with fallback products

            self.logger.info(f"Batch analysis completed: {len(results)} sections processed in single API call")
            return results

        except Exception as e:
            self.logger.error(f"Batch API call failed: {e}, falling back to individual calls")

            # Fallback to individual processing
            results = {}
            for section in sections:
                try:
                    products = await self.analyze_products(section['heading'], section['second_paragraph'])
                    results[section['heading']] = products
                    await asyncio.sleep(0.1)  # Rate limiting
                except Exception as section_error:
                    self.logger.error(f"Error processing section '{section['heading']}': {section_error}")
                    results[section['heading']] = []  # Return empty list instead of fallback products

            return results
    
    async def test_connection(self) -> bool:
        """
        Test the OpenAI API connection
        
        Returns:
            True if connection is successful
        """
        try:
            test_messages = [
                {"role": "user", "content": "Say 'test' if you can read this."}
            ]
            
            result = await self._make_request(test_messages)
            response_text = result['choices'][0]['message']['content'].strip().lower()
            
            success = 'test' in response_text
            if success:
                self.logger.info("OpenAI API connection test successful")
            else:
                self.logger.warning(f"OpenAI API connection test returned unexpected response: {response_text}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"OpenAI API connection test failed: {e}")
            return False
    
    def get_usage_info(self) -> dict:
        """
        Get information about API usage and configuration
        
        Returns:
            Dictionary with usage information
        """
        return {
            'model': OPENAI_MODEL,
            'max_tokens': OPENAI_MAX_TOKENS,
            'temperature': OPENAI_TEMPERATURE,
            'rate_limit_delay': self.rate_limit_delay,
            'api_key_configured': bool(self.api_key),
            'api_key_length': len(self.api_key) if self.api_key else 0
        }
