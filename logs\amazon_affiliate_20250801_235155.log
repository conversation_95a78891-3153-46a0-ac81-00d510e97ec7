2025-08-01 23:51:55,327 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250801_235155.log
2025-08-01 23:51:55,328 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250801_235155.log
2025-08-01 23:51:55,328 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-08-01 23:51:55,329 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-08-01 23:51:55,329 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-08-01 23:51:55,330 - __main__ - INFO - Starting complete workflow: Process → Crawl → Cleanup...
2025-08-01 23:51:55,330 - amazon_affiliate_integration - INFO - 🚀 Starting COMPLETE workflow: Process → Crawl → Cleanup
2025-08-01 23:51:55,330 - amazon_affiliate_integration - INFO - 📝 Phase 1: Processing articles to add Amazon shortcodes...
2025-08-01 23:51:55,330 - amazon_affiliate_integration - INFO - Starting domain processing: cozytones.com (force=False, dry_run=False, limit=1)
2025-08-01 23:51:55,331 - amazon_affiliate_integration - INFO - Loaded 0 previously processed URLs
2025-08-01 23:51:55,331 - amazon_affiliate_integration - INFO - Loaded 0 excluded URLs
2025-08-01 23:51:55,331 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress client initialized for cozytones.com (https://cozytones.com)
2025-08-01 23:51:57,372 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?per_page=1 "HTTP/1.1 200 OK"
2025-08-01 23:51:57,395 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress API connection test successful for cozytones.com
2025-08-01 23:51:57,395 - amazon_affiliate_integration - INFO - Fetching articles from cozytones.com...
2025-08-01 23:51:57,395 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetching articles from cozytones.com since 2025-01-01T00:00:00
2025-08-01 23:52:00,493 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?page=1&per_page=100&after=2025-01-01T00%3A00%3A00&status=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-08-01 23:52:01,795 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetched 100 articles from page 1
2025-08-01 23:52:01,795 - amazon_affiliate_integration.clients.wordpress_client - INFO - Limited to 1 articles for testing
2025-08-01 23:52:01,795 - amazon_affiliate_integration.clients.wordpress_client - INFO - Total articles fetched from cozytones.com: 1
2025-08-01 23:52:01,798 - amazon_affiliate_integration - INFO - Converted 1 articles to ArticleInfo objects
2025-08-01 23:52:01,798 - amazon_affiliate_integration - INFO - Normal mode: processing 1 articles (skipping 0 processed, 0 excluded)
2025-08-01 23:52:02,235 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Pre-processing checkpoint for cozytones.com (1 articles)
2025-08-01 23:52:02,236 - amazon_affiliate_integration - INFO - Created git checkpoint before processing cozytones.com
2025-08-01 23:52:02,236 - amazon_affiliate_integration - INFO - Processing 1 articles for cozytones.com...
2025-08-01 23:52:02,236 - amazon_affiliate_integration.processors.content_processor - INFO - Starting batch processing of 1 articles with concurrency limit 4
2025-08-01 23:52:02,236 - amazon_affiliate_integration.processors.content_processor - INFO - Processing article 304: 12 Modern Farmhouse Bedroom Ideas for Rustic Elegance
2025-08-01 23:52:02,240 - amazon_affiliate_integration.utils.backup_manager - INFO - Created backup: cozytones.com_304_pre_processing_20250801_235202.json
2025-08-01 23:52:02,240 - amazon_affiliate_integration.processors.content_processor - INFO - Generating products for 12 sections using batch processing
2025-08-01 23:52:02,241 - amazon_affiliate_integration.processors.content_processor - INFO - Using individual processing for reliable product generation
2025-08-01 23:52:04,749 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 23:52:04,750 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '1. Reclaimed Wood Headboard with Weathered Finish': ['Reclaimed Wood Headboard', 'Rustic Farmhouse Bed Frame']
2025-08-01 23:52:06,757 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 23:52:06,766 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '2. Industrial-Style Lighting with Matte Black Fixtures': ['Industrial Matte Black Pendant Light Fixture', 'Industrial Wall Sconce with Matte Black Finish']
2025-08-01 23:52:08,958 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 23:52:08,959 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '3. Linen Bedding in Soft Neutral Tones': ['Linen Duvet Cover Set', 'Wool Throw Blanket']
2025-08-01 23:52:13,957 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 23:52:13,963 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '4. Vintage-Inspired Nightstands with Distressed Paint': ['Vintage Distressed Wooden Nightstand', 'Hand-Painted Antique Style Nightstand']
2025-08-01 23:52:15,748 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 23:52:15,749 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '5. Neutral Area Rug with Subtle Pattern': ['Wool Area Rug', 'Handwoven Geometric Pattern Area Rug']
2025-08-01 23:52:17,377 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 23:52:17,384 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '6. Classic Barn Door Sliding Closet Entrance': ['Reclaimed Wood Sliding Barn Door', 'Black Iron Hardware for Barn Door']
2025-08-01 23:52:19,265 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 23:52:19,266 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '7. Minimalist Modern Bed Frame in Matte Finish': ['Minimalist Modern Matte Black Metal Bed Frame', 'Modern Low-Profile Platform Bed Frame in Soft Gray Finish']
2025-08-01 23:52:23,300 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 23:52:23,301 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '8. Mason Jar or Rustic Lanterns for Ambient Lighting': ['Mason Jar with LED Edison Bulb Set', 'Vintage Metal Frame Lantern with Candle LED Bulb']
2025-08-01 23:52:25,179 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 23:52:25,190 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '9. Simple Linen-Covered Window Treatments': ['Linen Window Curtain Panels', 'Adjustable Metal Curtain Rods']
2025-08-01 23:52:26,936 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 23:52:26,946 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '10. Layered Bedding with Cozy Throws and Pillows': ['Luxury Linen Bed Pillow Set', 'Chunky Knit Wool Throw Blanket']
2025-08-01 23:52:29,083 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 23:52:29,089 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '11. Framed Vintage Botanical Prints or Sketches': ['Framed Vintage Botanical Wall Art Prints', 'Distressed Wooden Frame for Wall Art']
2025-08-01 23:52:30,280 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-01 23:52:30,297 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '12. Practical yet Stylish Storage Solutions with Open Wardrobes': ['Wooden Clothing Rack with Distressed Finish', 'Open Wooden Shelving Unit with Hooks and Storage Baskets']
2025-08-01 23:52:30,403 - amazon_affiliate_integration.processors.content_processor - INFO - Inserted 12 product sections into content
2025-08-01 23:52:30,403 - amazon_affiliate_integration.processors.content_processor - INFO - Successfully processed article 304 in 28.17s. Added 12 shortcodes for 12 sections.
2025-08-01 23:52:30,404 - amazon_affiliate_integration.processors.content_processor - INFO - Batch processing completed: 1 successful, 0 failed, 0 skipped
2025-08-01 23:52:30,404 - amazon_affiliate_integration - INFO - Updating WordPress content for cozytones.com...
2025-08-01 23:52:30,404 - amazon_affiliate_integration.clients.wordpress_client - INFO - Starting batch update of 1 articles on cozytones.com
2025-08-01 23:52:32,104 - httpx - INFO - HTTP Request: POST https://cozytones.com/wp-json/wp/v2/posts/304 "HTTP/1.1 200 OK"
2025-08-01 23:52:32,211 - amazon_affiliate_integration.clients.wordpress_client - INFO - Successfully updated article 304 on cozytones.com
2025-08-01 23:52:32,212 - amazon_affiliate_integration.clients.wordpress_client - INFO - Batch update completed on cozytones.com: 1 successful, 0 failed
2025-08-01 23:52:32,212 - amazon_affiliate_integration - INFO - WordPress updates completed: 1 successful, 0 failed
2025-08-01 23:52:32,217 - amazon_affiliate_integration - INFO - Updated state with 1 newly processed URLs
2025-08-01 23:52:32,449 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Post-processing checkpoint for cozytones.com (1 articles processed)
2025-08-01 23:52:32,449 - amazon_affiliate_integration - INFO - Domain processing completed for cozytones.com: 1 processed, 0 failed, 0 skipped in 37.12s
2025-08-01 23:52:32,450 - amazon_affiliate_integration - INFO - ✅ Phase 1 completed successfully
2025-08-01 23:52:32,450 - amazon_affiliate_integration - INFO - 🕷️ Phase 2 & 3: Crawling and cleaning up failed shortcodes...
2025-08-01 23:52:32,450 - amazon_affiliate_integration - INFO - Starting crawl and cleanup workflow...
2025-08-01 23:52:32,450 - amazon_affiliate_integration - INFO - Phase 1: Crawling articles for visible shortcodes...
2025-08-01 23:52:32,451 - amazon_affiliate_integration - INFO - Using browser crawler for cozytones.com
2025-08-01 23:52:33,549 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser initialized with 4 concurrent pages
2025-08-01 23:52:33,549 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Starting browser crawl of 1 articles...
2025-08-01 23:52:44,351 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Progress: 1/1 articles crawled
2025-08-01 23:52:44,351 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser crawl completed: 1/1 successful in 10.80s (0.1 articles/sec)
2025-08-01 23:52:44,448 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser resources cleaned up
2025-08-01 23:52:44,448 - amazon_affiliate_integration - INFO - Crawled 1 articles for domain cozytones.com, found 0 visible shortcodes
2025-08-01 23:52:44,448 - amazon_affiliate_integration - INFO - Phase 2: Cleaning up failed shortcodes...
2025-08-01 23:52:44,449 - amazon_affiliate_integration - INFO - Crawl and cleanup workflow completed successfully in 12.00s
2025-08-01 23:52:44,449 - amazon_affiliate_integration - INFO - ✅ Phase 2 & 3 completed successfully
2025-08-01 23:52:44,449 - amazon_affiliate_integration - INFO - 🎉 COMPLETE workflow finished successfully in 49.12s
2025-08-01 23:52:44,449 - __main__ - INFO - Complete workflow finished successfully
