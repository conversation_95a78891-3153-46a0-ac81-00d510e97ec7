2025-08-02 11:09:39,481 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_110939.log
2025-08-02 11:09:39,482 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_110939.log
2025-08-02 11:09:39,482 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-08-02 11:09:39,482 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-08-02 11:09:39,483 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-08-02 11:09:39,493 - __main__ - INFO - Starting processing for domain: cozytones.com
2025-08-02 11:09:39,493 - amazon_affiliate_integration - INFO - Starting domain processing: cozytones.com (force=False, dry_run=False, limit=1)
2025-08-02 11:09:39,500 - amazon_affiliate_integration - INFO - Loaded 1 previously processed URLs
2025-08-02 11:09:39,500 - amazon_affiliate_integration - INFO - Loaded 0 excluded URLs
2025-08-02 11:09:39,500 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress client initialized for cozytones.com (https://cozytones.com)
2025-08-02 11:09:39,896 - httpcore.connection - DEBUG - connect_tcp.started host='cozytones.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:09:39,915 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000002C074A59AF0>
2025-08-02 11:09:39,915 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000002C074A2C7D0> server_hostname='cozytones.com' timeout=30.0
2025-08-02 11:09:39,930 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000002C0740EAED0>
2025-08-02 11:09:39,930 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-02 11:09:39,930 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:09:39,930 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-02 11:09:39,931 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:09:39,931 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 11:09:41,075 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:09:41 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'X-Wp-Total', b'139'), (b'X-Wp-Totalpages', b'139'), (b'Link', b'<https://cozytones.com/wp-json/wp/v2/posts?per_page=1&page=2>; rel="next"'), (b'Allow', b'GET, POST'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=IbjLfWjdAfdj8TNLk9ZpuTZwBBG3VoLjOJm3sDk7LZe7Z%2BiQbzEsetJCnIvALfVVA7Ef2nlgv225MGMCpksnMvv8E44AnT0nSFVte8k%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'968b107d0a862a54-DAC')])
2025-08-02 11:09:41,075 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?per_page=1 "HTTP/1.1 200 OK"
2025-08-02 11:09:41,076 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-02 11:09:41,077 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:09:41,077 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:09:41,077 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:09:41,078 - httpcore.connection - DEBUG - close.started
2025-08-02 11:09:41,078 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:09:41,078 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress API connection test successful for cozytones.com
2025-08-02 11:09:41,079 - amazon_affiliate_integration - INFO - Fetching articles from cozytones.com...
2025-08-02 11:09:41,079 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetching articles from cozytones.com since 2025-01-01T00:00:00
2025-08-02 11:09:41,292 - httpcore.connection - DEBUG - connect_tcp.started host='cozytones.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:09:41,301 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000002C074AB4F20>
2025-08-02 11:09:41,302 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000002C074A2F9D0> server_hostname='cozytones.com' timeout=30.0
2025-08-02 11:09:41,314 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000002C074A08F80>
2025-08-02 11:09:41,314 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-02 11:09:41,315 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:09:41,315 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-02 11:09:41,315 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:09:41,315 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 11:09:44,234 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:09:44 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'X-Wp-Total', b'139'), (b'X-Wp-Totalpages', b'2'), (b'Link', b'<https://cozytones.com/wp-json/wp/v2/posts?page=2&per_page=100&after=2025-01-01T00%3A00%3A00&status%5B0%5D=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit>; rel="next"'), (b'Allow', b'GET, POST'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=NV5C46u0r6bXW0jOS%2Bp%2BEbeDgPqFRuOwlnNC%2BqfwWANy24csJQCiSAxsmgELHad%2F2vNMjlvt3e%2Byae%2FEmZjfp1tEPXTnt4vy3pzbHHY%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'968b1085bdb2ded3-DAC')])
2025-08-02 11:09:44,235 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?page=1&per_page=100&after=2025-01-01T00%3A00%3A00&status=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-08-02 11:09:44,235 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-02 11:09:46,428 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:09:46,428 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:09:46,428 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:09:46,433 - httpcore.connection - DEBUG - close.started
2025-08-02 11:09:46,434 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:09:46,458 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetched 100 articles from page 1
2025-08-02 11:09:46,459 - amazon_affiliate_integration.clients.wordpress_client - INFO - Limited to 1 articles for testing
2025-08-02 11:09:46,459 - amazon_affiliate_integration.clients.wordpress_client - INFO - Total articles fetched from cozytones.com: 1
2025-08-02 11:09:46,462 - amazon_affiliate_integration.clients.wordpress_client - DEBUG - Converted 1 articles to ArticleInfo objects
2025-08-02 11:09:46,462 - amazon_affiliate_integration - INFO - Converted 1 articles to ArticleInfo objects
2025-08-02 11:09:46,462 - amazon_affiliate_integration - INFO - Normal mode: processing 0 articles (skipping 1 processed, 0 excluded)
2025-08-02 11:09:46,463 - amazon_affiliate_integration - INFO - No articles to process for cozytones.com after filtering
