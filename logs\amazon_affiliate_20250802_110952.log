2025-08-02 11:09:52,896 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_110952.log
2025-08-02 11:09:52,897 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_110952.log
2025-08-02 11:09:52,897 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-08-02 11:09:52,898 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-08-02 11:09:52,898 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-08-02 11:09:52,899 - __main__ - INFO - Starting processing for domain: cozytones.com
2025-08-02 11:09:52,899 - amazon_affiliate_integration - INFO - Starting domain processing: cozytones.com (force=True, dry_run=False, limit=1)
2025-08-02 11:09:52,899 - amazon_affiliate_integration - INFO - Force mode enabled: ignoring previously processed URLs
2025-08-02 11:09:52,900 - amazon_affiliate_integration - INFO - Loaded 0 excluded URLs
2025-08-02 11:09:52,900 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress client initialized for cozytones.com (https://cozytones.com)
2025-08-02 11:09:53,200 - httpcore.connection - DEBUG - connect_tcp.started host='cozytones.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:09:53,233 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001F5B7492E10>
2025-08-02 11:09:53,233 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001F5B7480050> server_hostname='cozytones.com' timeout=30.0
2025-08-02 11:09:53,248 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001F5B72FAFF0>
2025-08-02 11:09:53,249 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-02 11:09:53,249 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:09:53,250 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-02 11:09:53,250 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:09:53,250 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 11:09:53,614 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:09:53 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'X-Wp-Total', b'139'), (b'X-Wp-Totalpages', b'139'), (b'Link', b'<https://cozytones.com/wp-json/wp/v2/posts?per_page=1&page=2>; rel="next"'), (b'Allow', b'GET, POST'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=4ZpmqcZFFSzHIDXdZ1PfFi3BfhKmH%2FaELNz6DAm2eDtJnNiwodzjKbKIN%2BYFCODphPH6axIZphO66bcUoX3vGTO0ttfQIMnvjv4wKks%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'968b10d04b525942-DAC')])
2025-08-02 11:09:53,615 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?per_page=1 "HTTP/1.1 200 OK"
2025-08-02 11:09:53,615 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-02 11:09:53,616 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:09:53,617 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:09:53,617 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:09:53,617 - httpcore.connection - DEBUG - close.started
2025-08-02 11:09:53,618 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:09:53,618 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress API connection test successful for cozytones.com
2025-08-02 11:09:53,618 - amazon_affiliate_integration - INFO - Fetching articles from cozytones.com...
2025-08-02 11:09:53,618 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetching articles from cozytones.com since 2025-01-01T00:00:00
2025-08-02 11:09:53,827 - httpcore.connection - DEBUG - connect_tcp.started host='cozytones.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:09:53,839 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001F5B6B6C530>
2025-08-02 11:09:53,840 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x000001F5B74832D0> server_hostname='cozytones.com' timeout=30.0
2025-08-02 11:09:53,851 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x000001F5B7411760>
2025-08-02 11:09:53,851 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-02 11:09:53,851 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:09:53,852 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-02 11:09:53,852 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:09:53,852 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 11:09:56,634 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:09:56 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'X-Wp-Total', b'139'), (b'X-Wp-Totalpages', b'2'), (b'Link', b'<https://cozytones.com/wp-json/wp/v2/posts?page=2&per_page=100&after=2025-01-01T00%3A00%3A00&status%5B0%5D=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit>; rel="next"'), (b'Allow', b'GET, POST'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=RWKMIwxXgWd3TD39X1A3YNUStn%2F51TjaXNug%2FuwqPVXGvbez1MJAV3lfyWypa8FrTCDquQyNW21vvVUcqh11OCr8eNsjCb%2BQC1y%2BR7k%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'968b10d419642a54-DAC')])
2025-08-02 11:09:56,635 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?page=1&per_page=100&after=2025-01-01T00%3A00%3A00&status=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-08-02 11:09:56,635 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-02 11:09:58,082 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:09:58,083 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:09:58,083 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:09:58,087 - httpcore.connection - DEBUG - close.started
2025-08-02 11:09:58,087 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:09:58,107 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetched 100 articles from page 1
2025-08-02 11:09:58,108 - amazon_affiliate_integration.clients.wordpress_client - INFO - Limited to 1 articles for testing
2025-08-02 11:09:58,108 - amazon_affiliate_integration.clients.wordpress_client - INFO - Total articles fetched from cozytones.com: 1
2025-08-02 11:09:58,110 - amazon_affiliate_integration.clients.wordpress_client - DEBUG - Converted 1 articles to ArticleInfo objects
2025-08-02 11:09:58,110 - amazon_affiliate_integration - INFO - Converted 1 articles to ArticleInfo objects
2025-08-02 11:09:58,110 - amazon_affiliate_integration - INFO - Force mode: processing 1 articles (excluding 0 excluded)
2025-08-02 11:09:58,572 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Pre-processing checkpoint for cozytones.com (1 articles)
2025-08-02 11:09:58,572 - amazon_affiliate_integration - INFO - Created git checkpoint before processing cozytones.com
2025-08-02 11:09:58,572 - amazon_affiliate_integration - INFO - Processing 1 articles for cozytones.com...
2025-08-02 11:09:58,572 - amazon_affiliate_integration.processors.content_processor - INFO - Starting batch processing of 1 articles with concurrency limit 4
2025-08-02 11:09:58,573 - amazon_affiliate_integration.processors.content_processor - INFO - Processing article 304: 12 Modern Farmhouse Bedroom Ideas for Rustic Elegance
2025-08-02 11:09:58,577 - amazon_affiliate_integration.utils.backup_manager - INFO - Created backup: cozytones.com_304_pre_processing_20250802_110958.json
2025-08-02 11:09:58,578 - amazon_affiliate_integration.processors.content_processor - DEBUG - Parsed 0 H2 sections from content
2025-08-02 11:09:58,578 - amazon_affiliate_integration.processors.content_processor - WARNING - No H2 sections found in article 304
2025-08-02 11:09:58,579 - amazon_affiliate_integration.processors.content_processor - INFO - Batch processing completed: 0 successful, 0 failed, 1 skipped
2025-08-02 11:09:58,579 - amazon_affiliate_integration - INFO - Updating WordPress content for cozytones.com...
2025-08-02 11:09:58,791 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Post-processing checkpoint for cozytones.com (0 articles processed)
2025-08-02 11:09:58,791 - amazon_affiliate_integration - INFO - Domain processing completed for cozytones.com: 0 processed, 0 failed, 1 skipped in 5.89s
