2025-08-02 10:20:42,281 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_102042.log
2025-08-02 10:20:42,282 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_102042.log
2025-08-02 10:20:42,282 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-08-02 10:20:42,282 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-08-02 10:20:42,282 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-08-02 10:20:42,293 - __main__ - INFO - Starting crawl and cleanup workflow...
2025-08-02 10:20:42,293 - amazon_affiliate_integration - INFO - Starting crawl and cleanup workflow...
2025-08-02 10:20:42,310 - amazon_affiliate_integration - INFO - Phase 1: Crawling articles for visible shortcodes...
2025-08-02 10:20:42,326 - amazon_affiliate_integration - INFO - Using browser crawler for cozytones.com
2025-08-02 10:20:42,858 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser initialized with 4 concurrent pages
2025-08-02 10:20:42,858 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Starting browser crawl of 1 articles...
2025-08-02 10:20:45,446 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Content snippet for shortcode detection: <!DOCTYPE html><html lang="en-US" data-scroll="0"><head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>12 Modern Farmhouse Bedroom Ideas for Rustic Elegance</title>
<meta name="robots" content="max-image-preview:large">
	<style>img:is([sizes="auto" i], [sizes^="auto," i]) { contain-intrinsic-size: 3000px 1500px }</style>
	<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link href="https://fonts.gstatic.com" crossorigin="" rel="preconnect">
<link rel="alternate" type="application/rss+xml" title="Cozy Tones » Feed" href="https://cozytones.com/feed/">
<link rel="alternate" type="application/rss+xml" title="Cozy Tones » Comments Feed" href="https://cozytones.com/comments/feed/">
<link rel="alternate" type="application/rss+xml" title="Cozy Tones » 12 Modern Farmhouse Bedroom Ideas for Rustic Elegance Comments Feed" href="https://cozytones.com/modern-farmhouse-bedroom-ideas/feed/">
<link rel="canonical" href="https://cozytones.com/modern-farmhouse-bedroom-ideas/">
<script>
window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/16.0.1\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/16.0.1\/svg\/","svgExt":".svg","source":{"concatemoji":"https:\/\/cozytones.com\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.2"}};
/*! This file is auto-generated */
!function(s,n){var o,i,e;function c(e){try{var t={supportTests:e,timestamp:(new Date).valueOf()};sessionStorage.setItem(o,JSON.stringify(t))}catch(e){}}function p(e,t,n){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);var t=new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data),a=(e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(n,0,0),new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data));return t.every(function(e,t){return e===a[t]})}function u(e,t){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);for(var n=e.getImageData(16,16,1,1),a=0;a
2025-08-02 10:20:45,449 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found 0 shortcodes in HTML, 0 in text, 0 unique
2025-08-02 10:20:45,449 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - No visible shortcodes found in content
2025-08-02 10:20:45,454 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Progress: 1/1 articles crawled
2025-08-02 10:20:45,454 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser crawl completed: 1/1 successful in 2.60s (0.4 articles/sec)
2025-08-02 10:20:45,529 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser resources cleaned up
2025-08-02 10:20:45,529 - amazon_affiliate_integration - INFO - Crawled 1 articles for domain cozytones.com, found 0 visible shortcodes
2025-08-02 10:20:45,529 - amazon_affiliate_integration - INFO - Phase 2: Cleaning up failed shortcodes...
2025-08-02 10:20:45,530 - amazon_affiliate_integration - INFO - Crawl and cleanup workflow completed successfully in 3.22s
2025-08-02 10:20:45,530 - __main__ - INFO - Crawl and cleanup workflow completed successfully
