2025-08-02 11:04:39,994 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_110439.log
2025-08-02 11:04:39,996 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_110439.log
2025-08-02 11:04:39,996 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-08-02 11:04:39,996 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-08-02 11:04:39,996 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-08-02 11:04:39,996 - __main__ - INFO - Starting crawl and cleanup workflow...
2025-08-02 11:04:39,997 - amazon_affiliate_integration - INFO - Starting crawl and cleanup workflow...
2025-08-02 11:04:39,997 - amazon_affiliate_integration - INFO - Phase 1: Crawling articles for visible shortcodes...
2025-08-02 11:04:40,006 - amazon_affiliate_integration - INFO - Using browser crawler for cozytones.com
2025-08-02 11:04:40,681 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser initialized with 4 concurrent pages
2025-08-02 11:04:40,682 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Starting browser crawl of 1 articles...
2025-08-02 11:04:43,956 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Content snippet for shortcode detection: <!DOCTYPE html><html lang="en-US" data-scroll="0"><head>
<meta charset="UTF-8">
<meta name="viewport" content="width=device-width, initial-scale=1">
<title>12 Modern Farmhouse Bedroom Ideas for Rustic Elegance</title>
<meta name="robots" content="max-image-preview:large">
	<style>img:is([sizes="auto" i], [sizes^="auto," i]) { contain-intrinsic-size: 3000px 1500px }</style>
	<link rel="dns-prefetch" href="//fonts.googleapis.com">
<link href="https://fonts.gstatic.com" crossorigin="" rel="preconnect">
<link rel="alternate" type="application/rss+xml" title="Cozy Tones » Feed" href="https://cozytones.com/feed/">
<link rel="alternate" type="application/rss+xml" title="Cozy Tones » Comments Feed" href="https://cozytones.com/comments/feed/">
<link rel="alternate" type="application/rss+xml" title="Cozy Tones » 12 Modern Farmhouse Bedroom Ideas for Rustic Elegance Comments Feed" href="https://cozytones.com/modern-farmhouse-bedroom-ideas/feed/">
<link rel="canonical" href="https://cozytones.com/modern-farmhouse-bedroom-ideas/">
<script>
window._wpemojiSettings = {"baseUrl":"https:\/\/s.w.org\/images\/core\/emoji\/16.0.1\/72x72\/","ext":".png","svgUrl":"https:\/\/s.w.org\/images\/core\/emoji\/16.0.1\/svg\/","svgExt":".svg","source":{"concatemoji":"https:\/\/cozytones.com\/wp-includes\/js\/wp-emoji-release.min.js?ver=6.8.2"}};
/*! This file is auto-generated */
!function(s,n){var o,i,e;function c(e){try{var t={supportTests:e,timestamp:(new Date).valueOf()};sessionStorage.setItem(o,JSON.stringify(t))}catch(e){}}function p(e,t,n){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);var t=new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data),a=(e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(n,0,0),new Uint32Array(e.getImageData(0,0,e.canvas.width,e.canvas.height).data));return t.every(function(e,t){return e===a[t]})}function u(e,t){e.clearRect(0,0,e.canvas.width,e.canvas.height),e.fillText(t,0,0);for(var n=e.getImageData(16,16,1,1),a=0;a
2025-08-02 11:04:43,956 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Content contains 12 '[amazon bestseller=' strings
2025-08-02 11:04:43,957 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Regex pattern found 12 matches in HTML
2025-08-02 11:04:43,959 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found 12 shortcodes in HTML, 12 in text, 12 unique
2025-08-02 11:04:43,960 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Reclaimed Wood Headboard' under heading: 'Recommended Products to replicate this idea' at position 26976
2025-08-02 11:04:43,960 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Westinghouse Iron Hill 1-Light Pendant Light' under heading: 'Recommended Products to replicate this idea' at position 30250
2025-08-02 11:04:43,960 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Linen Duvet Cover Set' under heading: 'Recommended Products to replicate this idea' at position 33371
2025-08-02 11:04:43,960 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Vintage Distressed Nightstand' under heading: 'Recommended Products to replicate this idea' at position 36362
2025-08-02 11:04:43,961 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'NuLOOM Moroccan Blythe Area Rug' under heading: 'Recommended Products to replicate this idea' at position 39351
2025-08-02 11:04:43,961 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Reclaimed Wood Barn Door' under heading: 'Recommended Products to replicate this idea' at position 42256
2025-08-02 11:04:43,961 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Modern Matte Black Metal Bed Frame' under heading: 'Recommended Products to replicate this idea' at position 45026
2025-08-02 11:04:43,961 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Vintage Mason Jar Pendant Light Fixtures' under heading: 'Recommended Products to replicate this idea' at position 47921
2025-08-02 11:04:43,961 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Linen Window Curtains' under heading: 'Recommended Products to replicate this idea' at position 50976
2025-08-02 11:04:43,962 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Linen Throw Pillow Set' under heading: 'Recommended Products to replicate this idea' at position 53789
2025-08-02 11:04:43,962 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Vintage Botanical Framed Art Prints' under heading: 'Recommended Products to replicate this idea' at position 56645
2025-08-02 11:04:43,962 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - DEBUG - Found visible shortcode: 'Wooden Clothing Rack with Distressed Finish' under heading: 'Recommended Products to replicate this idea' at position 59510
2025-08-02 11:04:43,967 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Progress: 1/1 articles crawled
2025-08-02 11:04:43,968 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser crawl completed: 1/1 successful in 3.29s (0.3 articles/sec)
2025-08-02 11:04:44,086 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser resources cleaned up
2025-08-02 11:04:44,086 - amazon_affiliate_integration - INFO - Crawled 1 articles for domain cozytones.com, found 12 visible shortcodes
2025-08-02 11:04:44,088 - amazon_affiliate_integration.processors.state_manager - DEBUG - Created backup: D:\Amazon_Addon_For_Pinterest_Automation_v2\missing_products.bak.20250802_110444
2025-08-02 11:04:44,092 - amazon_affiliate_integration.processors.state_manager - DEBUG - Successfully wrote D:\Amazon_Addon_For_Pinterest_Automation_v2\missing_products.json
2025-08-02 11:04:44,093 - amazon_affiliate_integration - INFO - Phase 2: Cleaning up failed shortcodes...
2025-08-02 11:04:44,093 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress client initialized for cozytones.com (https://cozytones.com)
2025-08-02 11:04:44,094 - amazon_affiliate_integration.processors.cleanup_processor - INFO - Starting cleanup for 1 articles with visible shortcodes
2025-08-02 11:04:44,403 - httpcore.connection - DEBUG - connect_tcp.started host='cozytones.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:04:44,491 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000016506034380>
2025-08-02 11:04:44,492 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x0000016506118DD0> server_hostname='cozytones.com' timeout=30.0
2025-08-02 11:04:44,511 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000165061429C0>
2025-08-02 11:04:44,511 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'GET']>
2025-08-02 11:04:44,512 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:04:44,512 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'GET']>
2025-08-02 11:04:44,512 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:04:44,513 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'GET']>
2025-08-02 11:04:45,616 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:04:45 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'Link', b'<https://cozytones.com/wp-json/>; rel="https://api.w.org/"'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'X-Wp-Total', b'1'), (b'X-Wp-Totalpages', b'1'), (b'Allow', b'GET, POST'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=LA9fAvexgn9D2hwAMJhU03%2FdtKSlYgt2%2BZ2J75LXyN1uJfzqbbeqDhqTouQeEo2ttBOf95HOz6Bu5o3NDQFnnWN%2BnSnqts4uFcReC0A%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'968b0946ac0633bc-DAC')])
2025-08-02 11:04:45,618 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?slug=modern-farmhouse-bedroom-ideas&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-08-02 11:04:45,618 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'GET']>
2025-08-02 11:04:45,621 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:04:45,621 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:04:45,621 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:04:45,621 - httpcore.connection - DEBUG - close.started
2025-08-02 11:04:45,622 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:04:45,622 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Created cleanup backup: cleanup_backup_304_20250802_110445.json
2025-08-02 11:04:45,622 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Article content length: 37858
2025-08-02 11:04:45,623 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Visible shortcodes to remove: 12
2025-08-02 11:04:45,623 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   1. Product: 'Reclaimed Wood Headboard' | Full: '[amazon bestseller=”Reclaimed Wood Headboard” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 11:04:45,623 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   2. Product: 'Westinghouse Iron Hill 1-Light Pendant Light' | Full: '[amazon bestseller=”Westinghouse Iron Hill 1-Light Pendant Light” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 11:04:45,623 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   3. Product: 'Linen Duvet Cover Set' | Full: '[amazon bestseller=”Linen Duvet Cover Set” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 11:04:45,623 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   4. Product: 'Vintage Distressed Nightstand' | Full: '[amazon bestseller=”Vintage Distressed Nightstand” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 11:04:45,624 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   5. Product: 'NuLOOM Moroccan Blythe Area Rug' | Full: '[amazon bestseller=”NuLOOM Moroccan Blythe Area Rug” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 11:04:45,624 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   6. Product: 'Reclaimed Wood Barn Door' | Full: '[amazon bestseller=”Reclaimed Wood Barn Door” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 11:04:45,624 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   7. Product: 'Modern Matte Black Metal Bed Frame' | Full: '[amazon bestseller=”Modern Matte Black Metal Bed Frame” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 11:04:45,624 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   8. Product: 'Vintage Mason Jar Pendant Light Fixtures' | Full: '[amazon bestseller=”Vintage Mason Jar Pendant Light Fixtures” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 11:04:45,625 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   9. Product: 'Linen Window Curtains' | Full: '[amazon bestseller=”Linen Window Curtains” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 11:04:45,625 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   10. Product: 'Linen Throw Pillow Set' | Full: '[amazon bestseller=”Linen Throw Pillow Set” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 11:04:45,625 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   11. Product: 'Vintage Botanical Framed Art Prints' | Full: '[amazon bestseller=”Vintage Botanical Framed Art Prints” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 11:04:45,625 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG -   12. Product: 'Wooden Clothing Rack with Distressed Finish' | Full: '[amazon bestseller=”Wooden Clothing Rack with Distressed Finish” filterby=”price” filter=”1000″ filter_compare=”less” items=”1″ template=”table” tracking_id=”piatto-20″]'
2025-08-02 11:04:45,625 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - WordPress content contains 12 '[amazon' strings
2025-08-02 11:04:45,625 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Content snippet: 
<p>Imagine stepping into a bedroom that feels like a cozy retreat straight out of a farmhouse dream. Modern farmhouse bedrooms have surged in popularity because they blend rustic charm with contemporary comfort, creating spaces that are both inviting and stylish. This design trend captures the warmth of countryside living while offering the sleek, clean lines that fit perfectly into today’s modern homes, making it a favorite among homeowners seeking timeless appeal with a fresh twist.</p>



<p>In this article, you’ll discover a variety of inspiring ideas to transform your bedroom into a modern farmhouse haven. From charming color palettes and vintage accents to sleek furniture and cozy textiles, there’s something for every style preference. Get ready to explore creative ways to incorporate rustic elegance into your space and turn your bedroom into a peaceful, stylish sanctuary.</p>



<h2 class="wp-block-heading">1. Reclaimed Wood Headboard with Weathered Finish</h2>



<figure class="wp-block-image"><img decoding="async" src="https://cozytones.com/wp-content/uploads/pinterest/modern-farmhouse-bedroom-ideas/modern-farmhouse-bedroom-ideas-to-use-for-inspiration-1.jpg" alt=""/></figure>



<p>Ever feel like your bedroom lacks character and warmth? A dull or generic headboard can make the space feel uninspired and disconnected from that cozy farmhouse vibe you crave. The idea of a simple, rustic focal point is appealing, but finding one that fits your style and budget can be tricky. DIY or upcycling reclaimed wood turns this challenge into an opportunity for authenticity. It’s the perfect way to add personality without breaking the bank.</p>



<h3 class="wp-block-heading">Recommended Products to replicate this idea</h3>

<p>[amazon bestseller=&#8221;Reclaimed Wood Headboard&#8221; filterby=&#8221;price&#8221; filter=&#8221;1000&#8243; filter_compare=&#8221;less&#8221; items=&#8221;1&#8243; template=&#8221;table&#8221; tracking_id=&#8221;piatto-20&#8243;]</p>




<p>
2025-08-02 11:04:45,625 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Starting shortcode removal from content (37858 chars)
2025-08-02 11:04:45,626 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Looking for 12 visible shortcodes
2025-08-02 11:04:45,626 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Wooden Clothing Rack with Distressed Finish'
2025-08-02 11:04:45,626 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 11:04:45,626 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Wooden Clothing Rack with Distressed Finish', trying shortcode-only pattern
2025-08-02 11:04:45,626 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 11:04:45,626 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Shortcode-only removal failed for 'Wooden Clothing Rack with Distressed Finish', trying direct shortcode pattern
2025-08-02 11:04:45,628 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 1 direct shortcode matches for 'Wooden Clothing Rack with Distressed Finish'
2025-08-02 11:04:45,628 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found matching direct shortcode for product 'Wooden Clothing Rack with Distressed Finish' at position 35223-35476
2025-08-02 11:04:45,628 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Removed direct shortcode for product 'Wooden Clothing Rack with Distressed Finish'
2025-08-02 11:04:45,628 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Vintage Botanical Framed Art Prints'
2025-08-02 11:04:45,628 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 11:04:45,628 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Vintage Botanical Framed Art Prints', trying shortcode-only pattern
2025-08-02 11:04:45,628 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 11:04:45,628 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Shortcode-only removal failed for 'Vintage Botanical Framed Art Prints', trying direct shortcode pattern
2025-08-02 11:04:45,629 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 1 direct shortcode matches for 'Vintage Botanical Framed Art Prints'
2025-08-02 11:04:45,629 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found matching direct shortcode for product 'Vintage Botanical Framed Art Prints' at position 32273-32518
2025-08-02 11:04:45,629 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Removed direct shortcode for product 'Vintage Botanical Framed Art Prints'
2025-08-02 11:04:45,629 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Linen Throw Pillow Set'
2025-08-02 11:04:45,629 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 11:04:45,630 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Linen Throw Pillow Set', trying shortcode-only pattern
2025-08-02 11:04:45,630 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 11:04:45,630 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Shortcode-only removal failed for 'Linen Throw Pillow Set', trying direct shortcode pattern
2025-08-02 11:04:45,630 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 1 direct shortcode matches for 'Linen Throw Pillow Set'
2025-08-02 11:04:45,631 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found matching direct shortcode for product 'Linen Throw Pillow Set' at position 29332-29564
2025-08-02 11:04:45,631 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Removed direct shortcode for product 'Linen Throw Pillow Set'
2025-08-02 11:04:45,631 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Linen Window Curtains'
2025-08-02 11:04:45,632 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 11:04:45,632 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Linen Window Curtains', trying shortcode-only pattern
2025-08-02 11:04:45,632 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 11:04:45,632 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Shortcode-only removal failed for 'Linen Window Curtains', trying direct shortcode pattern
2025-08-02 11:04:45,632 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 1 direct shortcode matches for 'Linen Window Curtains'
2025-08-02 11:04:45,633 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found matching direct shortcode for product 'Linen Window Curtains' at position 26434-26665
2025-08-02 11:04:45,633 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Removed direct shortcode for product 'Linen Window Curtains'
2025-08-02 11:04:45,633 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Vintage Mason Jar Pendant Light Fixtures'
2025-08-02 11:04:45,633 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 11:04:45,633 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Vintage Mason Jar Pendant Light Fixtures', trying shortcode-only pattern
2025-08-02 11:04:45,634 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 11:04:45,634 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Shortcode-only removal failed for 'Vintage Mason Jar Pendant Light Fixtures', trying direct shortcode pattern
2025-08-02 11:04:45,634 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 1 direct shortcode matches for 'Vintage Mason Jar Pendant Light Fixtures'
2025-08-02 11:04:45,634 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found matching direct shortcode for product 'Vintage Mason Jar Pendant Light Fixtures' at position 23294-23544
2025-08-02 11:04:45,634 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Removed direct shortcode for product 'Vintage Mason Jar Pendant Light Fixtures'
2025-08-02 11:04:45,635 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Modern Matte Black Metal Bed Frame'
2025-08-02 11:04:45,635 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 11:04:45,635 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Modern Matte Black Metal Bed Frame', trying shortcode-only pattern
2025-08-02 11:04:45,635 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 11:04:45,635 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Shortcode-only removal failed for 'Modern Matte Black Metal Bed Frame', trying direct shortcode pattern
2025-08-02 11:04:45,636 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 1 direct shortcode matches for 'Modern Matte Black Metal Bed Frame'
2025-08-02 11:04:45,636 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found matching direct shortcode for product 'Modern Matte Black Metal Bed Frame' at position 20314-20558
2025-08-02 11:04:45,636 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Removed direct shortcode for product 'Modern Matte Black Metal Bed Frame'
2025-08-02 11:04:45,636 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Reclaimed Wood Barn Door'
2025-08-02 11:04:45,636 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 11:04:45,636 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Reclaimed Wood Barn Door', trying shortcode-only pattern
2025-08-02 11:04:45,637 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 11:04:45,637 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Shortcode-only removal failed for 'Reclaimed Wood Barn Door', trying direct shortcode pattern
2025-08-02 11:04:45,637 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 1 direct shortcode matches for 'Reclaimed Wood Barn Door'
2025-08-02 11:04:45,637 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found matching direct shortcode for product 'Reclaimed Wood Barn Door' at position 17459-17693
2025-08-02 11:04:45,638 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Removed direct shortcode for product 'Reclaimed Wood Barn Door'
2025-08-02 11:04:45,638 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'NuLOOM Moroccan Blythe Area Rug'
2025-08-02 11:04:45,638 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 11:04:45,638 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'NuLOOM Moroccan Blythe Area Rug', trying shortcode-only pattern
2025-08-02 11:04:45,638 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 11:04:45,638 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Shortcode-only removal failed for 'NuLOOM Moroccan Blythe Area Rug', trying direct shortcode pattern
2025-08-02 11:04:45,639 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 1 direct shortcode matches for 'NuLOOM Moroccan Blythe Area Rug'
2025-08-02 11:04:45,639 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found matching direct shortcode for product 'NuLOOM Moroccan Blythe Area Rug' at position 14469-14710
2025-08-02 11:04:45,639 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Removed direct shortcode for product 'NuLOOM Moroccan Blythe Area Rug'
2025-08-02 11:04:45,641 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Vintage Distressed Nightstand'
2025-08-02 11:04:45,641 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 11:04:45,641 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Vintage Distressed Nightstand', trying shortcode-only pattern
2025-08-02 11:04:45,641 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 11:04:45,641 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Shortcode-only removal failed for 'Vintage Distressed Nightstand', trying direct shortcode pattern
2025-08-02 11:04:45,642 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 1 direct shortcode matches for 'Vintage Distressed Nightstand'
2025-08-02 11:04:45,642 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found matching direct shortcode for product 'Vintage Distressed Nightstand' at position 11395-11634
2025-08-02 11:04:45,642 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Removed direct shortcode for product 'Vintage Distressed Nightstand'
2025-08-02 11:04:45,642 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Linen Duvet Cover Set'
2025-08-02 11:04:45,642 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 11:04:45,642 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Linen Duvet Cover Set', trying shortcode-only pattern
2025-08-02 11:04:45,642 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 11:04:45,642 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Shortcode-only removal failed for 'Linen Duvet Cover Set', trying direct shortcode pattern
2025-08-02 11:04:45,643 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 1 direct shortcode matches for 'Linen Duvet Cover Set'
2025-08-02 11:04:45,644 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found matching direct shortcode for product 'Linen Duvet Cover Set' at position 8319-8550
2025-08-02 11:04:45,644 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Removed direct shortcode for product 'Linen Duvet Cover Set'
2025-08-02 11:04:45,644 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Westinghouse Iron Hill 1-Light Pendant Light'
2025-08-02 11:04:45,644 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 11:04:45,645 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Westinghouse Iron Hill 1-Light Pendant Light', trying shortcode-only pattern
2025-08-02 11:04:45,645 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 11:04:45,645 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Shortcode-only removal failed for 'Westinghouse Iron Hill 1-Light Pendant Light', trying direct shortcode pattern
2025-08-02 11:04:45,645 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 1 direct shortcode matches for 'Westinghouse Iron Hill 1-Light Pendant Light'
2025-08-02 11:04:45,646 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found matching direct shortcode for product 'Westinghouse Iron Hill 1-Light Pendant Light' at position 5113-5367
2025-08-02 11:04:45,646 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Removed direct shortcode for product 'Westinghouse Iron Hill 1-Light Pendant Light'
2025-08-02 11:04:45,646 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Processing shortcode for product: 'Reclaimed Wood Headboard'
2025-08-02 11:04:45,646 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode blocks in content
2025-08-02 11:04:45,646 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Block removal failed for 'Reclaimed Wood Headboard', trying shortcode-only pattern
2025-08-02 11:04:45,646 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 0 shortcode-only matches
2025-08-02 11:04:45,647 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Shortcode-only removal failed for 'Reclaimed Wood Headboard', trying direct shortcode pattern
2025-08-02 11:04:45,647 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found 1 direct shortcode matches for 'Reclaimed Wood Headboard'
2025-08-02 11:04:45,648 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Found matching direct shortcode for product 'Reclaimed Wood Headboard' at position 1754-1988
2025-08-02 11:04:45,648 - amazon_affiliate_integration.processors.cleanup_processor - DEBUG - Removed direct shortcode for product 'Reclaimed Wood Headboard'
2025-08-02 11:04:45,932 - httpcore.connection - DEBUG - connect_tcp.started host='cozytones.com' port=443 local_address=None timeout=30.0 socket_options=None
2025-08-02 11:04:45,943 - httpcore.connection - DEBUG - connect_tcp.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x00000165061AAB40>
2025-08-02 11:04:45,945 - httpcore.connection - DEBUG - start_tls.started ssl_context=<ssl.SSLContext object at 0x00000165061924D0> server_hostname='cozytones.com' timeout=30.0
2025-08-02 11:04:45,959 - httpcore.connection - DEBUG - start_tls.complete return_value=<httpcore._backends.anyio.AnyIOStream object at 0x0000016506143E90>
2025-08-02 11:04:45,960 - httpcore.http11 - DEBUG - send_request_headers.started request=<Request [b'POST']>
2025-08-02 11:04:45,960 - httpcore.http11 - DEBUG - send_request_headers.complete
2025-08-02 11:04:45,960 - httpcore.http11 - DEBUG - send_request_body.started request=<Request [b'POST']>
2025-08-02 11:04:45,961 - httpcore.http11 - DEBUG - send_request_body.complete
2025-08-02 11:04:45,961 - httpcore.http11 - DEBUG - receive_response_headers.started request=<Request [b'POST']>
2025-08-02 11:04:46,426 - httpcore.http11 - DEBUG - receive_response_headers.complete return_value=(b'HTTP/1.1', 200, b'OK', [(b'Date', b'Sat, 02 Aug 2025 05:04:46 GMT'), (b'Content-Type', b'application/json; charset=UTF-8'), (b'Transfer-Encoding', b'chunked'), (b'Connection', b'keep-alive'), (b'Server', b'cloudflare'), (b'Vary', b'Accept-Encoding'), (b'X-Robots-Tag', b'noindex'), (b'Link', b'<https://cozytones.com/wp-json/>; rel="https://api.w.org/"'), (b'X-Content-Type-Options', b'nosniff'), (b'X-Content-Type-Options', b'nosniff'), (b'Access-Control-Expose-Headers', b'X-WP-Total, X-WP-TotalPages, Link'), (b'Access-Control-Allow-Headers', b'Authorization, X-WP-Nonce, Content-Disposition, Content-MD5, Content-Type'), (b'Allow', b'GET, POST, PUT, PATCH, DELETE'), (b'Expires', b'Wed, 11 Jan 1984 05:00:00 GMT'), (b'Cache-Control', b'no-cache, must-revalidate, max-age=0, no-store, private'), (b'X-Frame-Options', b'SAMEORIGIN'), (b'X-Xss-Protection', b'1; mode=block'), (b'X-Permitted-Cross-Domain-Policies', b'master-only'), (b'Referrer-Policy', b'same-origin'), (b'Alt-Svc', b'h3=":443"; ma=86400'), (b'Report-To', b'{"group":"cf-nel","max_age":604800,"endpoints":[{"url":"https://a.nel.cloudflare.com/report/v4?s=X2PVVpojJX6AebO84I%2BZGb8%2BgkX1s0n%2B2z8oZve1RwlG0zhitM8BfYrErWGhwkwbemwgzqcNp0ZGt7v40GVctVS%2BIUDrYyUL%2FiOgw0M%3D"}]}'), (b'Cf-Cache-Status', b'DYNAMIC'), (b'Nel', b'{"report_to":"cf-nel","success_fraction":0.0,"max_age":604800}'), (b'Content-Encoding', b'gzip'), (b'CF-RAY', b'968b094fbd32f257-DAC')])
2025-08-02 11:04:46,427 - httpx - INFO - HTTP Request: POST https://cozytones.com/wp-json/wp/v2/posts/304 "HTTP/1.1 200 OK"
2025-08-02 11:04:46,427 - httpcore.http11 - DEBUG - receive_response_body.started request=<Request [b'POST']>
2025-08-02 11:04:46,431 - httpcore.http11 - DEBUG - receive_response_body.complete
2025-08-02 11:04:46,432 - httpcore.http11 - DEBUG - response_closed.started
2025-08-02 11:04:46,432 - httpcore.http11 - DEBUG - response_closed.complete
2025-08-02 11:04:46,433 - httpcore.connection - DEBUG - close.started
2025-08-02 11:04:46,433 - httpcore.connection - DEBUG - close.complete
2025-08-02 11:04:46,433 - amazon_affiliate_integration.clients.wordpress_client - INFO - Successfully updated article 304 on cozytones.com
2025-08-02 11:04:46,436 - amazon_affiliate_integration.processors.state_manager - DEBUG - Created backup: D:\Amazon_Addon_For_Pinterest_Automation_v2\missing_products.bak.20250802_110446
2025-08-02 11:04:46,438 - amazon_affiliate_integration.processors.state_manager - DEBUG - Successfully wrote D:\Amazon_Addon_For_Pinterest_Automation_v2\missing_products.json
2025-08-02 11:04:46,438 - amazon_affiliate_integration.processors.cleanup_processor - INFO - Successfully cleaned up 12 shortcodes from https://cozytones.com/modern-farmhouse-bedroom-ideas/
2025-08-02 11:04:46,438 - amazon_affiliate_integration.processors.cleanup_processor - INFO - Cleaned up 1/1 articles (12 shortcodes removed)
2025-08-02 11:04:46,438 - amazon_affiliate_integration.processors.cleanup_processor - INFO - Cleanup completed: {'articles_processed': 1, 'shortcodes_removed': 12, 'cleanup_failures': 0, 'articles_updated': 1}
2025-08-02 11:04:46,438 - amazon_affiliate_integration - INFO - Crawl and cleanup workflow completed successfully in 6.44s
2025-08-02 11:04:46,438 - __main__ - INFO - Crawl and cleanup workflow completed successfully
