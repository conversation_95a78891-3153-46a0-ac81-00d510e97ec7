# Failed Product Section Cleanup

This document describes the enhanced cleanup functionality for handling AAWP (Amazon Affiliate WordPress Plugin) failures in the Amazon Affiliate Integration system.

## Overview

When AAWP plugin fails to render product recommendations, it can leave unprofessional-looking content in articles. This system detects and cleans up these failures automatically.

## Failure Scenarios

### Scenario 1: AAWP Deactivated/Crashed
**Symptoms:**
- Raw shortcode text is visible in the rendered HTML
- Example: `[amazon bestseller="modern bathroom vanity" filterby="price"...]`

**Detection:**
- Detected by crawlers that find visible shortcode text in rendered HTML
- Cannot be detected from WordPress content alone

**Cleanup:**
- Handled by existing `cleanup_failed_shortcodes()` method
- Removes entire PRODUCT_SECTION_TEMPLATE (H3 heading + shortcode)
- No additional code needed

### Scenario 2b: AAWP Active But No Products Found
**Symptoms:**
- H3 heading "Recommended Products to replicate this idea" is present
- Shortcode disappears completely (AAWP hides it when no products found)
- H3 is followed immediately by paragraph content

**Detection:**
- Detected from WordPress content using regex pattern
- Looks for H3 followed immediately by `<!-- wp:paragraph -->`

**Cleanup:**
- Handled by new `cleanup_failed_product_sections()` method
- Removes orphaned H3 headings

## Implementation

### New Methods Added

#### `cleanup_failed_product_sections(content: str) -> tuple`
Removes orphaned H3 headings when shortcodes disappear.

```python
cleaned_content, sections_removed = processor.cleanup_failed_product_sections(content)
```

#### `cleanup_failed_product_sections_batch(article_urls: List[str]) -> List[CleanupResult]`
Processes multiple articles for failed product section cleanup.

```python
results = await processor.cleanup_failed_product_sections_batch(article_urls)
```

### Detection Pattern

The system uses this regex pattern to detect failed product sections:

```python
FAILED_PRODUCT_SECTION_PATTERN = re.compile(
    r'<!-- wp:heading \{"level":3\} -->\s*'
    r'<h3 class="wp-block-heading">Recommended Products to replicate this idea</h3>\s*'
    r'<!-- /wp:heading -->\s*'
    r'(?=<!-- wp:paragraph -->)',
    re.IGNORECASE | re.MULTILINE
)
```

This pattern matches:
1. Gutenberg heading block with level 3
2. H3 element with specific text and class
3. Closing heading block
4. Followed immediately by paragraph block (lookahead)

## Usage Examples

### Basic Usage

```python
from amazon_affiliate_integration.processors.cleanup_processor import CleanupProcessor

# Initialize processor
processor = CleanupProcessor(wordpress_client, state_manager)

# Clean up failed product sections from multiple articles
article_urls = ["https://example.com/article1/", "https://example.com/article2/"]
results = await processor.cleanup_failed_product_sections_batch(article_urls)

# Process results
successful = [r for r in results if r.success]
print(f"Successfully cleaned {len(successful)} articles")
```

### Content-Only Processing

```python
# Direct content processing (no WordPress API calls)
processor = CleanupProcessor(None, None)
cleaned_content, sections_removed = processor.cleanup_failed_product_sections(content)
```

### Comprehensive Workflow

```python
# Step 1: Handle Scenario 1 (existing functionality)
crawler_results = await crawler.crawl_articles(article_urls)
scenario_1_results = await processor.cleanup_failed_shortcodes(crawler_results)

# Step 2: Handle Scenario 2b (new functionality)
scenario_2b_results = await processor.cleanup_failed_product_sections_batch(article_urls)
```

## Testing

Run the test suite to verify functionality:

```bash
python test_failed_product_cleanup.py
```

The test suite validates:
- ✅ Scenario 2b detection and cleanup
- ✅ Working section preservation
- ✅ Multiple failed sections handling
- ✅ Pattern accuracy

## Live Validation

The approach was validated against live website content:
- **Site:** https://cozytones.com/teen-bedroom-decor-ideas/
- **Section 11:** Shows exact failure mode (H3 followed by paragraph)
- **Result:** Pattern detects failure perfectly

## Integration Points

### With Existing Crawler System
```python
# Crawlers detect Scenario 1 (visible shortcodes)
crawler_results = await browser_crawler.crawl_articles(urls)
visible_shortcode_results = await processor.cleanup_failed_shortcodes(crawler_results)
```

### With Content Processing Pipeline
```python
# Add to content processing workflow
async def process_article_cleanup(article_url):
    # Handle both scenarios
    await processor.cleanup_failed_shortcodes(crawler_results)  # Scenario 1
    await processor.cleanup_failed_product_sections_batch([article_url])  # Scenario 2b
```

### With State Management
```python
# Cleanup updates state automatically
results = await processor.cleanup_failed_product_sections_batch(urls)
# State manager tracks processed articles and removed products
```

## Configuration

No additional configuration required. The system uses existing:
- WordPress credentials from `WP_CREDENTIALS`
- Concurrency limits from `CONCURRENCY_LIMIT`
- Backup settings from `AFFILIATE_BACKUP_DIR`

## Logging

The system provides detailed logging:
- INFO: Summary of cleanup operations
- DEBUG: Detailed pattern matching and content processing
- ERROR: Failures and exceptions

## Error Handling

- Graceful handling of WordPress API failures
- Automatic backup creation before cleanup
- Comprehensive error logging
- Partial success reporting (some articles may fail while others succeed)

## Performance

- Async processing with configurable concurrency
- Batch operations for multiple articles
- Efficient regex patterns
- Progress reporting for long-running operations

## Monitoring

Track cleanup effectiveness using:
```python
stats = processor.get_statistics()
print(f"Articles processed: {stats['articles_processed']}")
print(f"Sections removed: {stats['shortcodes_removed']}")
print(f"Success rate: {stats['articles_updated']}/{stats['articles_processed']}")
```
