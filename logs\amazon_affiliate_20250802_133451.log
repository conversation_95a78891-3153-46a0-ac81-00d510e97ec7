2025-08-02 13:34:51,044 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_133451.log
2025-08-02 13:34:51,045 - amazon_affiliate_integration - INFO - Logging initialized. Log file: logs\amazon_affiliate_20250802_133451.log
2025-08-02 13:34:51,045 - amazon_affiliate_integration.clients.openai_client - INFO - OpenAI client initialized with model: gpt-4.1-nano
2025-08-02 13:34:51,046 - amazon_affiliate_integration.processors.content_processor - INFO - ContentProcessor initialized with concurrency limit: 4
2025-08-02 13:34:51,046 - amazon_affiliate_integration - INFO - Enhanced orchestrator initialized with concurrency limit: 4
2025-08-02 13:34:51,049 - __main__ - INFO - Starting complete workflow: Process → Crawl → Cleanup...
2025-08-02 13:34:51,049 - amazon_affiliate_integration - INFO - 🚀 Starting COMPLETE workflow: Process → Crawl → Cleanup
2025-08-02 13:34:51,049 - amazon_affiliate_integration - INFO - 📝 Phase 1: Processing articles to add Amazon shortcodes...
2025-08-02 13:34:51,049 - amazon_affiliate_integration - INFO - Starting domain processing: cozytones.com (force=False, dry_run=False, limit=1)
2025-08-02 13:34:51,049 - amazon_affiliate_integration - INFO - Loaded 0 previously processed URLs
2025-08-02 13:34:51,050 - amazon_affiliate_integration - INFO - Loaded 0 excluded URLs
2025-08-02 13:34:51,050 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress client initialized for cozytones.com (https://cozytones.com)
2025-08-02 13:34:52,669 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?per_page=1 "HTTP/1.1 200 OK"
2025-08-02 13:34:52,671 - amazon_affiliate_integration.clients.wordpress_client - INFO - WordPress API connection test successful for cozytones.com
2025-08-02 13:34:52,671 - amazon_affiliate_integration - INFO - Fetching articles from cozytones.com...
2025-08-02 13:34:52,671 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetching articles from cozytones.com since 2025-01-01T00:00:00
2025-08-02 13:34:55,928 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?page=1&per_page=100&after=2025-01-01T00%3A00%3A00&status=publish&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-08-02 13:34:57,264 - amazon_affiliate_integration.clients.wordpress_client - INFO - Fetched 100 articles from page 1
2025-08-02 13:34:57,265 - amazon_affiliate_integration.clients.wordpress_client - INFO - Limited to 1 articles for testing
2025-08-02 13:34:57,265 - amazon_affiliate_integration.clients.wordpress_client - INFO - Total articles fetched from cozytones.com: 1
2025-08-02 13:34:57,267 - amazon_affiliate_integration - INFO - Converted 1 articles to ArticleInfo objects
2025-08-02 13:34:57,268 - amazon_affiliate_integration - INFO - Normal mode: processing 1 articles (skipping 0 processed, 0 excluded)
2025-08-02 13:34:57,629 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Pre-processing checkpoint for cozytones.com (1 articles)
2025-08-02 13:34:57,629 - amazon_affiliate_integration - INFO - Created git checkpoint before processing cozytones.com
2025-08-02 13:34:57,629 - amazon_affiliate_integration - INFO - Processing 1 articles for cozytones.com...
2025-08-02 13:34:57,630 - amazon_affiliate_integration.processors.content_processor - INFO - Starting batch processing of 1 articles with concurrency limit 4
2025-08-02 13:34:57,630 - amazon_affiliate_integration.processors.content_processor - INFO - Processing article 304: 12 Modern Farmhouse Bedroom Ideas for Rustic Elegance
2025-08-02 13:34:57,633 - amazon_affiliate_integration.utils.backup_manager - INFO - Created backup: cozytones.com_304_pre_processing_20250802_133457.json
2025-08-02 13:34:57,635 - amazon_affiliate_integration.processors.content_processor - INFO - Generating products for 12 sections using batch processing
2025-08-02 13:34:57,635 - amazon_affiliate_integration.processors.content_processor - INFO - Using individual processing for reliable product generation
2025-08-02 13:34:59,271 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:34:59,272 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '1. Reclaimed Wood Headboard with Weathered Finish': ['Reclaimed Wood Headboard', 'Rustic Farmhouse Bed Frame']
2025-08-02 13:35:01,182 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:35:01,188 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '2. Industrial-Style Lighting with Matte Black Fixtures': ['Kichler 42344OZ Adorne 4" Square LED Wall Sconce', 'Progress Lighting P2900-09 Antique Bronze Pendant Light Fixture']
2025-08-02 13:35:02,822 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:35:02,823 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '3. Linen Bedding in Soft Neutral Tones': ['Linen Duvet Cover Set', 'Luxury Wool Throw Blanket']
2025-08-02 13:35:04,763 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:35:04,768 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '4. Vintage-Inspired Nightstands with Distressed Paint': ['Vintage-Inspired Distressed Nightstand', 'Rustic Wooden Bedside Table']
2025-08-02 13:35:06,333 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:35:06,334 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '5. Neutral Area Rug with Subtle Pattern': ['Plush Beige Area Rug', 'Light Gray Geometric Pattern Area Rug']
2025-08-02 13:35:08,121 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:35:08,126 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '6. Classic Barn Door Sliding Closet Entrance': ['Reclaimed Wood Barn Door', 'Black Iron Hardware Sliding Barn Door Track Kit']
2025-08-02 13:35:09,675 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:35:09,677 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '7. Minimalist Modern Bed Frame in Matte Finish': ['Minimalist Modern Metal Bed Frame', 'Matte Finish Platform Bed with Storage']
2025-08-02 13:35:11,134 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:35:11,138 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '8. Mason Jar or Rustic Lanterns for Ambient Lighting': ['Mason Jar String Lights with Edison Bulbs', 'Rustic Metal Lanterns with Candle-Style LED Bulbs']
2025-08-02 13:35:12,163 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:35:12,169 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '9. Simple Linen-Covered Window Treatments': ['Linen Window Curtain Panels', 'Rustic Wood Window Curtain Rods']
2025-08-02 13:35:13,606 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:35:13,609 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '10. Layered Bedding with Cozy Throws and Pillows': ['Luxury Quilted Bedding Set', 'Chunky Knit Throw Blanket']
2025-08-02 13:35:15,104 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:35:15,107 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '11. Framed Vintage Botanical Prints or Sketches': ['Vintage Botanical Art Print Framed Wall Decor', 'Distressed Wood Frame for Botanical Prints']
2025-08-02 13:35:16,278 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-08-02 13:35:16,284 - amazon_affiliate_integration.processors.content_processor - INFO - Generated products for '12. Practical yet Stylish Storage Solutions with Open Wardrobes': ['Wooden Clothing Rack with Distressed Finish', 'Open Wooden Shelving Unit for Clothing and Accessories']
2025-08-02 13:35:16,393 - amazon_affiliate_integration.processors.content_processor - INFO - Inserted 24 product sections into content
2025-08-02 13:35:16,393 - amazon_affiliate_integration.processors.content_processor - INFO - Successfully processed article 304 in 18.76s. Added 24 shortcodes for 12 sections.
2025-08-02 13:35:16,394 - amazon_affiliate_integration.processors.content_processor - INFO - Batch processing completed: 1 successful, 0 failed, 0 skipped
2025-08-02 13:35:16,394 - amazon_affiliate_integration - INFO - Updating WordPress content for cozytones.com...
2025-08-02 13:35:16,395 - amazon_affiliate_integration.clients.wordpress_client - INFO - Starting batch update of 1 articles on cozytones.com
2025-08-02 13:35:18,371 - httpx - INFO - HTTP Request: POST https://cozytones.com/wp-json/wp/v2/posts/304 "HTTP/1.1 200 OK"
2025-08-02 13:35:18,376 - amazon_affiliate_integration.clients.wordpress_client - INFO - Successfully updated article 304 on cozytones.com
2025-08-02 13:35:18,376 - amazon_affiliate_integration.clients.wordpress_client - INFO - Batch update completed on cozytones.com: 1 successful, 0 failed
2025-08-02 13:35:18,376 - amazon_affiliate_integration - INFO - WordPress updates completed: 1 successful, 0 failed
2025-08-02 13:35:18,379 - amazon_affiliate_integration - INFO - Updated state with 1 newly processed URLs
2025-08-02 13:35:18,629 - amazon_affiliate_integration.utils.backup_manager - INFO - Created git checkpoint: Post-processing checkpoint for cozytones.com (1 articles processed)
2025-08-02 13:35:18,629 - amazon_affiliate_integration - INFO - Domain processing completed for cozytones.com: 1 processed, 0 failed, 0 skipped in 27.58s
2025-08-02 13:35:18,630 - amazon_affiliate_integration - INFO - ✅ Phase 1 completed successfully
2025-08-02 13:35:18,630 - amazon_affiliate_integration - INFO - 🕷️ Phase 2 & 3: Crawling and cleaning up failed shortcodes...
2025-08-02 13:35:18,630 - amazon_affiliate_integration - INFO - Starting crawl and cleanup workflow...
2025-08-02 13:35:18,630 - amazon_affiliate_integration - INFO - Phase 1: Crawling articles for visible shortcodes...
2025-08-02 13:35:18,631 - amazon_affiliate_integration - INFO - Using browser crawler for cozytones.com
2025-08-02 13:35:19,187 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser initialized with 4 concurrent pages
2025-08-02 13:35:19,188 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Starting browser crawl of 1 articles...
2025-08-02 13:35:27,268 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - Progress: 1/1 articles crawled
2025-08-02 13:35:27,269 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser crawl completed: 1/1 successful in 8.08s (0.1 articles/sec)
2025-08-02 13:35:27,369 - amazon_affiliate_integration.crawlers.browser_shortcode_crawler - INFO - ✓ Browser resources cleaned up
2025-08-02 13:35:27,369 - amazon_affiliate_integration - INFO - Crawled 1 articles for domain cozytones.com, found 24 visible shortcodes
2025-08-02 13:35:27,372 - amazon_affiliate_integration - INFO - Phase 2: Cleaning up failed shortcodes...
2025-08-02 13:35:27,372 - amazon_affiliate_integration.processors.cleanup_processor - INFO - Starting cleanup for 1 articles with visible shortcodes
2025-08-02 13:35:28,801 - httpx - INFO - HTTP Request: GET https://cozytones.com/wp-json/wp/v2/posts?slug=modern-farmhouse-bedroom-ideas&_fields=id%2Ctitle%2Ccontent%2Cdate%2Cmodified%2Cslug%2Clink&context=edit "HTTP/1.1 200 OK"
2025-08-02 13:35:29,938 - httpx - INFO - HTTP Request: POST https://cozytones.com/wp-json/wp/v2/posts/304 "HTTP/1.1 200 OK"
2025-08-02 13:35:29,943 - amazon_affiliate_integration.clients.wordpress_client - INFO - Successfully updated article 304 on cozytones.com
2025-08-02 13:35:29,947 - amazon_affiliate_integration.processors.cleanup_processor - INFO - Successfully cleaned up 23 shortcodes from https://cozytones.com/modern-farmhouse-bedroom-ideas/
2025-08-02 13:35:29,947 - amazon_affiliate_integration.processors.cleanup_processor - INFO - Cleaned up 1/1 articles (23 shortcodes removed)
2025-08-02 13:35:29,947 - amazon_affiliate_integration.processors.cleanup_processor - INFO - Cleanup completed: {'articles_processed': 1, 'shortcodes_removed': 23, 'cleanup_failures': 0, 'articles_updated': 1}
2025-08-02 13:35:29,947 - amazon_affiliate_integration - INFO - Crawl and cleanup workflow completed successfully in 11.32s
2025-08-02 13:35:29,947 - amazon_affiliate_integration - INFO - ✅ Phase 2 & 3 completed successfully
2025-08-02 13:35:29,948 - amazon_affiliate_integration - INFO - 🎉 COMPLETE workflow finished successfully in 38.90s
2025-08-02 13:35:29,948 - __main__ - INFO - Complete workflow finished successfully
